/* CSS变量定义 */
:root {
    --primary-color: #667eea;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-color: #f093fb;
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-color: #4ade80;
    --warning-color: #fbbf24;
    --error-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --sidebar-width: 320px;
    --header-height: 70px;
    --input-height: 60px;
}

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* 应用容器 */
.app-container {
    display: flex;
    height: 100vh;
    position: relative;
}

/* 侧边栏样式 */
.sidebar {
    width: var(--sidebar-width);
    background: var(--surface-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    box-shadow: var(--shadow-md);
    z-index: 100;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--primary-gradient);
    color: white;
}

.sidebar-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: background-color 0.2s;
    display: none;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* 配置区域 */
.config-section,
.json-section,
.history-section {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.config-section h3,
.json-section h3,
.history-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
}

.form-group {
    margin-bottom: 1rem;
    position: relative;
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    background: var(--surface-color);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.toggle-password {
    position: absolute;
    right: 0.75rem;
    top: 2.25rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    justify-content: center;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--background-color);
    border-color: var(--primary-color);
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

.btn-send {
    background: var(--primary-gradient);
    color: white;
}

.btn-send:hover {
    transform: scale(1.05);
}

/* JSON文件列表 */
.json-files-list {
    margin-top: 1rem;
}

.json-file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--background-color);
    border-radius: var(--radius-md);
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
}

.json-file-item:hover {
    background: var(--border-color);
    transform: translateX(2px);
}

.json-file-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.json-file-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.json-file-size {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.json-file-actions {
    display: flex;
    gap: 0.25rem;
}

/* 历史控制按钮 */
.history-controls {
    display: flex;
    gap: 0.5rem;
}

.history-controls .btn {
    flex: 1;
    font-size: 0.75rem;
    padding: 0.5rem;
}

/* 主聊天区域 */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--background-color);
    position: relative;
}

/* 聊天头部 */
.chat-header {
    height: var(--header-height);
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    box-shadow: var(--shadow-sm);
}

.chat-title h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-dot.connecting {
    background: var(--warning-color);
}

.status-dot.error {
    background: var(--error-color);
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 欢迎消息 */
.welcome-message {
    text-align: center;
    max-width: 500px;
    margin: auto;
    padding: 3rem 2rem;
}

.welcome-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.welcome-message h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 1rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-message p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.welcome-message ul {
    text-align: left;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.welcome-message li {
    margin-bottom: 0.5rem;
}

/* 消息样式 */
.message {
    display: flex;
    gap: 1rem;
    max-width: 800px;
    animation: fadeInUp 0.3s ease;
}

.message.user {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--secondary-gradient);
    color: white;
}

.message.assistant .message-avatar {
    background: var(--primary-gradient);
    color: white;
}

.message-content {
    flex: 1;
    background: var(--surface-color);
    padding: 1rem 1.25rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    position: relative;
}

.message.user .message-content {
    background: var(--primary-gradient);
    color: white;
}

.message-content::before {
    content: '';
    position: absolute;
    top: 1rem;
    width: 0;
    height: 0;
    border: 8px solid transparent;
}

.message.user .message-content::before {
    right: -16px;
    border-left-color: var(--primary-color);
}

.message.assistant .message-content::before {
    left: -16px;
    border-right-color: var(--surface-color);
}

.message-text {
    line-height: 1.6;
    word-wrap: break-word;
}

.message-text pre {
    background: rgba(0, 0, 0, 0.1);
    padding: 1rem;
    border-radius: var(--radius-md);
    overflow-x: auto;
    margin: 0.5rem 0;
}

.message-text code {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: var(--radius-sm);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.5rem;
    opacity: 0.7;
}

.message.user .message-time {
    color: rgba(255, 255, 255, 0.8);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 输入区域 */
.chat-input-container {
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
    box-shadow: var(--shadow-md);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 1rem;
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: 0.75rem 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    resize: none;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
    max-height: 120px;
    min-height: 24px;
}

#messageInput::placeholder {
    color: var(--text-muted);
}

.input-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.input-footer {
    margin-top: 0.75rem;
}

.attached-context {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
}

.context-label {
    font-weight: 500;
}

.context-files {
    flex: 1;
}

.remove-context {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: background-color 0.2s;
}

.remove-context:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s;
}

.modal-close:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

.modal-body {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--border-color);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* JSON查看器样式 */
.json-viewer-controls {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.json-content {
    position: relative;
}

#jsonDisplay {
    background: #1e293b;
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

#jsonEditor {
    width: 100%;
    height: 400px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    resize: vertical;
    background: var(--surface-color);
}

#jsonEditor:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通知样式 */
.notification-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1500;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.notification {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 1rem 1.25rem;
    min-width: 300px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slideInRight 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

.notification.error {
    border-left-color: var(--error-color);
}

.notification-icon {
    font-size: 1.25rem;
    color: var(--primary-color);
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification.error .notification-icon {
    color: var(--error-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.notification-message {
    font-size: 0.8125rem;
    color: var(--text-secondary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s;
}

.notification-close:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --sidebar-width: 280px;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        transform: translateX(-100%);
        z-index: 200;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .sidebar-toggle {
        display: block;
    }

    .chat-header {
        padding: 0 1rem;
    }

    .chat-messages {
        padding: 1rem;
    }

    .chat-input-container {
        padding: 1rem;
    }

    .message {
        max-width: 100%;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .notification-container {
        top: 1rem;
        right: 1rem;
        left: 1rem;
    }

    .notification {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .chat-header h1 {
        font-size: 1.25rem;
    }

    .welcome-message {
        padding: 2rem 1rem;
    }

    .welcome-icon {
        font-size: 3rem;
    }

    .welcome-message h2 {
        font-size: 1.5rem;
    }

    .input-wrapper {
        padding: 0.5rem 0.75rem;
    }

    .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8125rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* 选择文本样式 */
::selection {
    background: rgba(102, 126, 234, 0.2);
}

/* 焦点样式 */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 禁用状态 */
.btn:disabled,
input:disabled,
select:disabled,
textarea:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 动画性能优化 */
* {
    will-change: auto;
}

.message,
.notification,
.modal-content {
    will-change: transform, opacity;
}

/* 打印样式 */
@media print {
    .sidebar,
    .chat-header,
    .chat-input-container,
    .modal,
    .loading-overlay,
    .notification-container {
        display: none !important;
    }

    .chat-messages {
        padding: 0;
    }

    .message-content::before {
        display: none;
    }
}
