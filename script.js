// AI聊天应用主脚本
class AIChat {
    constructor() {
        this.messages = [];
        this.jsonFiles = new Map();
        this.attachedContext = [];
        this.config = this.loadConfig();
        this.isTyping = false;
        
        this.initializeElements();
        this.bindEvents();
        this.loadChatHistory();
        this.updateAPIConfig();
    }
    
    // 初始化DOM元素引用
    initializeElements() {
        // 侧边栏元素
        this.sidebar = document.getElementById('sidebar');
        this.sidebarToggle = document.getElementById('sidebarToggle');
        
        // API配置元素
        this.apiProvider = document.getElementById('apiProvider');
        this.apiKey = document.getElementById('apiKey');
        this.apiEndpoint = document.getElementById('apiEndpoint');
        this.modelName = document.getElementById('modelName');
        this.saveConfigBtn = document.getElementById('saveConfig');
        this.toggleApiKeyBtn = document.getElementById('toggleApiKey');
        
        // JSON文件元素
        this.jsonFileInput = document.getElementById('jsonFileInput');
        this.uploadJsonBtn = document.getElementById('uploadJsonBtn');
        this.jsonFilesList = document.getElementById('jsonFilesList');
        
        // 聊天元素
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendMessageBtn = document.getElementById('sendMessageBtn');
        this.attachJsonBtn = document.getElementById('attachJsonBtn');
        this.newChatBtn = document.getElementById('newChatBtn');
        this.statusIndicator = document.getElementById('statusIndicator');
        
        // 上下文元素
        this.attachedContext = document.getElementById('attachedContext');
        this.contextFiles = document.getElementById('contextFiles');
        this.removeContext = document.getElementById('removeContext');
        
        // 历史管理元素
        this.clearHistoryBtn = document.getElementById('clearHistory');
        this.exportHistoryBtn = document.getElementById('exportHistory');
        
        // 模态框元素
        this.jsonViewerModal = document.getElementById('jsonViewerModal');
        this.closeJsonViewer = document.getElementById('closeJsonViewer');
        this.jsonViewerTitle = document.getElementById('jsonViewerTitle');
        this.jsonDisplay = document.getElementById('jsonDisplay');
        this.jsonEditor = document.getElementById('jsonEditor');
        this.editJsonBtn = document.getElementById('editJsonBtn');
        this.formatJsonBtn = document.getElementById('formatJsonBtn');
        this.validateJsonBtn = document.getElementById('validateJsonBtn');
        this.saveJsonBtn = document.getElementById('saveJsonBtn');
        this.cancelEditBtn = document.getElementById('cancelEditBtn');
        
        // 加载和通知元素
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.notificationContainer = document.getElementById('notificationContainer');
    }
    
    // 绑定事件监听器
    bindEvents() {
        // 侧边栏切换
        this.sidebarToggle?.addEventListener('click', () => this.toggleSidebar());
        
        // API配置事件
        this.apiProvider.addEventListener('change', () => this.updateAPIDefaults());
        this.saveConfigBtn.addEventListener('click', () => this.saveConfig());
        this.toggleApiKeyBtn.addEventListener('click', () => this.toggleApiKeyVisibility());
        
        // JSON文件事件
        this.uploadJsonBtn.addEventListener('click', () => this.jsonFileInput.click());
        this.jsonFileInput.addEventListener('change', (e) => this.handleJsonUpload(e));
        
        // 聊天事件
        this.sendMessageBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keydown', (e) => this.handleInputKeydown(e));
        this.messageInput.addEventListener('input', () => this.adjustTextareaHeight());
        this.attachJsonBtn.addEventListener('click', () => this.showJsonAttachMenu());
        this.newChatBtn.addEventListener('click', () => this.startNewChat());
        
        // 上下文事件
        this.removeContext.addEventListener('click', () => this.clearAttachedContext());
        
        // 历史管理事件
        this.clearHistoryBtn.addEventListener('click', () => this.clearChatHistory());
        this.exportHistoryBtn.addEventListener('click', () => this.exportChatHistory());
        
        // 模态框事件
        this.closeJsonViewer.addEventListener('click', () => this.closeJsonViewerModal());
        this.editJsonBtn.addEventListener('click', () => this.toggleJsonEdit());
        this.formatJsonBtn.addEventListener('click', () => this.formatJson());
        this.validateJsonBtn.addEventListener('click', () => this.validateJson());
        this.saveJsonBtn.addEventListener('click', () => this.saveJsonEdit());
        this.cancelEditBtn.addEventListener('click', () => this.cancelJsonEdit());
        
        // 模态框外部点击关闭
        this.jsonViewerModal.addEventListener('click', (e) => {
            if (e.target === this.jsonViewerModal) {
                this.closeJsonViewerModal();
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleGlobalKeydown(e));
        
        // 窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
    }
    
    // 加载配置
    loadConfig() {
        const defaultConfig = {
            apiProvider: 'openai',
            apiKey: '',
            apiEndpoint: 'https://api.openai.com/v1',
            modelName: 'gpt-3.5-turbo'
        };
        
        try {
            const saved = localStorage.getItem('aiChatConfig');
            return saved ? { ...defaultConfig, ...JSON.parse(saved) } : defaultConfig;
        } catch (error) {
            console.error('加载配置失败:', error);
            return defaultConfig;
        }
    }
    
    // 保存配置
    saveConfig() {
        const config = {
            apiProvider: this.apiProvider.value,
            apiKey: this.apiKey.value,
            apiEndpoint: this.apiEndpoint.value,
            modelName: this.modelName.value
        };
        
        try {
            localStorage.setItem('aiChatConfig', JSON.stringify(config));
            this.config = config;
            this.showNotification('配置已保存', '设置已成功保存到本地存储', 'success');
            this.updateStatus('已配置', 'ready');
        } catch (error) {
            console.error('保存配置失败:', error);
            this.showNotification('保存失败', '无法保存配置到本地存储', 'error');
        }
    }
    
    // 更新API默认值
    updateAPIDefaults() {
        const provider = this.apiProvider.value;
        const defaults = {
            openai: {
                endpoint: 'https://api.openai.com/v1',
                model: 'gpt-3.5-turbo'
            },
            claude: {
                endpoint: 'https://api.anthropic.com/v1',
                model: 'claude-3-sonnet-20240229'
            },
            deepseek: {
                endpoint: 'https://api.deepseek.com',
                model: 'deepseek-chat'
            },
            'deepseek-r1': {
                endpoint: 'https://api.deepseek.com',
                model: 'deepseek-reasoner'
            },
            gemini: {
                endpoint: 'https://generativelanguage.googleapis.com/v1beta',
                model: 'gemini-pro'
            },
            'gemini-2-flash': {
                endpoint: 'https://generativelanguage.googleapis.com/v1beta',
                model: 'gemini-2.0-flash'
            },
            qwen: {
                endpoint: 'https://dashscope.aliyuncs.com/api/v1',
                model: 'qwen-turbo'
            },
            custom: {
                endpoint: '',
                model: ''
            }
        };

        const config = defaults[provider];
        if (config) {
            this.apiEndpoint.value = config.endpoint;
            this.modelName.value = config.model;
        }
    }
    
    // 更新API配置显示
    updateAPIConfig() {
        this.apiProvider.value = this.config.apiProvider;
        this.apiKey.value = this.config.apiKey;
        this.apiEndpoint.value = this.config.apiEndpoint;
        this.modelName.value = this.config.modelName;
    }
    
    // 切换API密钥可见性
    toggleApiKeyVisibility() {
        const isPassword = this.apiKey.type === 'password';
        this.apiKey.type = isPassword ? 'text' : 'password';
        this.toggleApiKeyBtn.innerHTML = isPassword ? 
            '<i class="fas fa-eye-slash"></i>' : 
            '<i class="fas fa-eye"></i>';
    }
    
    // 切换侧边栏
    toggleSidebar() {
        this.sidebar.classList.toggle('open');
    }
    
    // 处理窗口大小变化
    handleResize() {
        if (window.innerWidth > 768) {
            this.sidebar.classList.remove('open');
        }
    }
    
    // 调整文本框高度
    adjustTextareaHeight() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }
    
    // 处理输入框键盘事件
    handleInputKeydown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
    }
    
    // 处理全局键盘事件
    handleGlobalKeydown(e) {
        // Ctrl/Cmd + K 聚焦到输入框
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.messageInput.focus();
        }
        
        // Escape 关闭模态框
        if (e.key === 'Escape') {
            this.closeJsonViewerModal();
        }
    }
    
    // 更新状态指示器
    updateStatus(text, type = 'ready') {
        const statusText = this.statusIndicator.querySelector('.status-text');
        const statusDot = this.statusIndicator.querySelector('.status-dot');

        statusText.textContent = text;
        statusDot.className = `status-dot ${type}`;
    }

    // 显示通知
    showNotification(title, message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const iconMap = {
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle',
            info: 'fas fa-info-circle'
        };

        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${iconMap[type]}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });

        this.notificationContainer.appendChild(notification);

        // 自动移除通知
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }
    }

    // 显示加载状态
    showLoading(show = true) {
        this.loadingOverlay.style.display = show ? 'flex' : 'none';
    }

    // JSON文件上传处理
    async handleJsonUpload(event) {
        const files = Array.from(event.target.files);

        for (const file of files) {
            if (!file.name.toLowerCase().endsWith('.json')) {
                this.showNotification('文件格式错误', `${file.name} 不是有效的JSON文件`, 'warning');
                continue;
            }

            try {
                const content = await this.readFileAsText(file);
                const jsonData = JSON.parse(content);

                const fileInfo = {
                    name: file.name,
                    size: file.size,
                    content: jsonData,
                    uploadTime: new Date().toISOString()
                };

                this.jsonFiles.set(file.name, fileInfo);
                this.updateJsonFilesList();
                this.showNotification('上传成功', `${file.name} 已成功上传`, 'success');

            } catch (error) {
                console.error('JSON文件解析失败:', error);
                this.showNotification('解析失败', `${file.name} 不是有效的JSON格式`, 'error');
            }
        }

        // 清空文件输入
        event.target.value = '';
    }

    // 读取文件内容
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    }

    // 更新JSON文件列表显示
    updateJsonFilesList() {
        this.jsonFilesList.innerHTML = '';

        if (this.jsonFiles.size === 0) {
            this.jsonFilesList.innerHTML = '<p style="color: var(--text-muted); font-size: 0.875rem; text-align: center; padding: 1rem;">暂无JSON文件</p>';
            return;
        }

        this.jsonFiles.forEach((fileInfo, fileName) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'json-file-item';

            fileItem.innerHTML = `
                <div class="json-file-info">
                    <div class="json-file-name">${fileName}</div>
                    <div class="json-file-size">${this.formatFileSize(fileInfo.size)}</div>
                </div>
                <div class="json-file-actions">
                    <button class="btn-icon view-json" title="查看">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon attach-json" title="附加到对话">
                        <i class="fas fa-paperclip"></i>
                    </button>
                    <button class="btn-icon delete-json" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            // 绑定事件
            fileItem.querySelector('.view-json').addEventListener('click', () => {
                this.viewJsonFile(fileName);
            });

            fileItem.querySelector('.attach-json').addEventListener('click', () => {
                this.attachJsonToContext(fileName);
            });

            fileItem.querySelector('.delete-json').addEventListener('click', () => {
                this.deleteJsonFile(fileName);
            });

            this.jsonFilesList.appendChild(fileItem);
        });
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 查看JSON文件
    viewJsonFile(fileName) {
        const fileInfo = this.jsonFiles.get(fileName);
        if (!fileInfo) return;

        this.currentEditingFile = fileName;
        this.jsonViewerTitle.textContent = `JSON查看器 - ${fileName}`;
        this.jsonDisplay.textContent = JSON.stringify(fileInfo.content, null, 2);
        this.jsonEditor.value = JSON.stringify(fileInfo.content, null, 2);

        // 重置编辑状态
        this.jsonDisplay.style.display = 'block';
        this.jsonEditor.style.display = 'none';
        this.editJsonBtn.innerHTML = '<i class="fas fa-edit"></i> 编辑';
        this.saveJsonBtn.style.display = 'none';
        this.cancelEditBtn.style.display = 'none';

        this.showJsonViewerModal();
    }

    // 显示JSON查看器模态框
    showJsonViewerModal() {
        this.jsonViewerModal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    // 关闭JSON查看器模态框
    closeJsonViewerModal() {
        this.jsonViewerModal.classList.remove('show');
        document.body.style.overflow = '';
        this.currentEditingFile = null;
    }

    // 切换JSON编辑模式
    toggleJsonEdit() {
        const isEditing = this.jsonEditor.style.display !== 'none';

        if (isEditing) {
            // 退出编辑模式
            this.jsonDisplay.style.display = 'block';
            this.jsonEditor.style.display = 'none';
            this.editJsonBtn.innerHTML = '<i class="fas fa-edit"></i> 编辑';
            this.saveJsonBtn.style.display = 'none';
            this.cancelEditBtn.style.display = 'none';
        } else {
            // 进入编辑模式
            this.jsonDisplay.style.display = 'none';
            this.jsonEditor.style.display = 'block';
            this.editJsonBtn.innerHTML = '<i class="fas fa-eye"></i> 预览';
            this.saveJsonBtn.style.display = 'inline-flex';
            this.cancelEditBtn.style.display = 'inline-flex';
            this.jsonEditor.focus();
        }
    }

    // 格式化JSON
    formatJson() {
        try {
            const isEditing = this.jsonEditor.style.display !== 'none';
            if (isEditing) {
                const content = JSON.parse(this.jsonEditor.value);
                this.jsonEditor.value = JSON.stringify(content, null, 2);
            } else {
                const content = JSON.parse(this.jsonDisplay.textContent);
                this.jsonDisplay.textContent = JSON.stringify(content, null, 2);
            }
            this.showNotification('格式化成功', 'JSON已格式化', 'success', 2000);
        } catch (error) {
            this.showNotification('格式化失败', 'JSON格式不正确', 'error');
        }
    }

    // 验证JSON
    validateJson() {
        try {
            const isEditing = this.jsonEditor.style.display !== 'none';
            const content = isEditing ? this.jsonEditor.value : this.jsonDisplay.textContent;
            JSON.parse(content);
            this.showNotification('验证成功', 'JSON格式正确', 'success', 2000);
        } catch (error) {
            this.showNotification('验证失败', `JSON格式错误: ${error.message}`, 'error');
        }
    }

    // 保存JSON编辑
    saveJsonEdit() {
        try {
            const content = JSON.parse(this.jsonEditor.value);
            const fileInfo = this.jsonFiles.get(this.currentEditingFile);

            if (fileInfo) {
                fileInfo.content = content;
                this.jsonFiles.set(this.currentEditingFile, fileInfo);
                this.jsonDisplay.textContent = JSON.stringify(content, null, 2);
                this.toggleJsonEdit(); // 退出编辑模式
                this.showNotification('保存成功', 'JSON文件已更新', 'success');
            }
        } catch (error) {
            this.showNotification('保存失败', `JSON格式错误: ${error.message}`, 'error');
        }
    }

    // 取消JSON编辑
    cancelJsonEdit() {
        const fileInfo = this.jsonFiles.get(this.currentEditingFile);
        if (fileInfo) {
            this.jsonEditor.value = JSON.stringify(fileInfo.content, null, 2);
        }
        this.toggleJsonEdit(); // 退出编辑模式
    }

    // 删除JSON文件
    deleteJsonFile(fileName) {
        if (confirm(`确定要删除 ${fileName} 吗？`)) {
            this.jsonFiles.delete(fileName);
            this.updateJsonFilesList();

            // 如果该文件在上下文中，也要移除
            this.attachedContext = this.attachedContext.filter(name => name !== fileName);
            this.updateAttachedContextDisplay();

            this.showNotification('删除成功', `${fileName} 已删除`, 'success');
        }
    }

    // 附加JSON到上下文
    attachJsonToContext(fileName) {
        if (!this.attachedContext.includes(fileName)) {
            this.attachedContext.push(fileName);
            this.updateAttachedContextDisplay();
            this.showNotification('附加成功', `${fileName} 已添加到对话上下文`, 'success', 2000);
        } else {
            this.showNotification('已存在', `${fileName} 已在上下文中`, 'warning', 2000);
        }
    }

    // 更新附加上下文显示
    updateAttachedContextDisplay() {
        if (this.attachedContext.length === 0) {
            this.attachedContext.style.display = 'none';
        } else {
            this.attachedContext.style.display = 'flex';
            this.contextFiles.textContent = this.attachedContext.join(', ');
        }
    }

    // 清除附加上下文
    clearAttachedContext() {
        this.attachedContext = [];
        this.updateAttachedContextDisplay();
        this.showNotification('已清除', '上下文已清空', 'info', 2000);
    }

    // 显示JSON附加菜单
    showJsonAttachMenu() {
        if (this.jsonFiles.size === 0) {
            this.showNotification('无可用文件', '请先上传JSON文件', 'warning');
            return;
        }

        // 创建简单的选择菜单
        const fileNames = Array.from(this.jsonFiles.keys());
        const availableFiles = fileNames.filter(name => !this.attachedContext.includes(name));

        if (availableFiles.length === 0) {
            this.showNotification('无可附加文件', '所有JSON文件都已在上下文中', 'info');
            return;
        }

        // 简单实现：附加第一个可用文件
        this.attachJsonToContext(availableFiles[0]);
    }

    // 发送消息
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isTyping) return;

        // 检查API配置
        if (!this.config.apiKey) {
            this.showNotification('配置错误', '请先配置API密钥', 'warning');
            return;
        }

        // 添加用户消息
        this.addMessage('user', message);
        this.messageInput.value = '';
        this.adjustTextareaHeight();

        // 准备上下文数据
        const contextData = this.prepareContextData();

        try {
            this.isTyping = true;
            this.updateStatus('AI正在思考...', 'connecting');
            this.showLoading(true);

            // 调用AI API
            const response = await this.callAIAPI(message, contextData);

            // 添加AI回复
            this.addMessage('assistant', response);
            this.updateStatus('就绪', 'ready');

        } catch (error) {
            console.error('AI API调用失败:', error);
            this.addMessage('assistant', '抱歉，我遇到了一些问题。请检查网络连接和API配置。');
            this.updateStatus('错误', 'error');
            this.showNotification('请求失败', error.message, 'error');
        } finally {
            this.isTyping = false;
            this.showLoading(false);
        }
    }

    // 准备上下文数据
    prepareContextData() {
        const contextData = {};

        this.attachedContext.forEach(fileName => {
            const fileInfo = this.jsonFiles.get(fileName);
            if (fileInfo) {
                contextData[fileName] = fileInfo.content;
            }
        });

        return contextData;
    }

    // 调用AI API
    async callAIAPI(message, contextData) {
        const { apiProvider, apiKey, apiEndpoint, modelName } = this.config;

        // 构建消息历史
        const messages = this.buildMessageHistory(message, contextData);

        // 根据不同的API提供商构建请求
        switch (apiProvider) {
            case 'openai':
                return await this.callOpenAIAPI(messages, apiKey, apiEndpoint, modelName);
            case 'claude':
                return await this.callClaudeAPI(messages, apiKey, apiEndpoint, modelName);
            case 'deepseek':
            case 'deepseek-r1':
                return await this.callDeepSeekAPI(messages, apiKey, apiEndpoint, modelName);
            case 'gemini':
            case 'gemini-2-flash':
                return await this.callGeminiAPI(messages, apiKey, apiEndpoint, modelName);
            case 'qwen':
                return await this.callQwenAPI(messages, apiKey, apiEndpoint, modelName);
            default:
                return await this.callGenericAPI(messages, apiKey, apiEndpoint, modelName);
        }
    }

    // 构建消息历史
    buildMessageHistory(currentMessage, contextData) {
        const messages = [];

        // 添加系统提示（如果有上下文数据）
        if (Object.keys(contextData).length > 0) {
            const contextPrompt = `以下是用户提供的JSON上下文数据，请在回答时参考这些信息：\n\n${JSON.stringify(contextData, null, 2)}`;
            messages.push({
                role: 'system',
                content: contextPrompt
            });
        }

        // 添加历史消息（最近10条）
        const recentMessages = this.messages.slice(-10);
        recentMessages.forEach(msg => {
            messages.push({
                role: msg.role,
                content: msg.content
            });
        });

        // 添加当前消息
        messages.push({
            role: 'user',
            content: currentMessage
        });

        return messages;
    }

    // OpenAI API调用
    async callOpenAIAPI(messages, apiKey, endpoint, model) {
        const response = await fetch(`${endpoint}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: model,
                messages: messages,
                temperature: 0.7,
                max_tokens: 2000
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error?.message || `HTTP ${response.status}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    // Claude API调用
    async callClaudeAPI(messages, apiKey, endpoint, model) {
        // 将messages格式转换为Claude格式
        const systemMessage = messages.find(m => m.role === 'system');
        const userMessages = messages.filter(m => m.role !== 'system');

        const response = await fetch(`${endpoint}/messages`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': apiKey,
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: model,
                max_tokens: 2000,
                system: systemMessage?.content || '',
                messages: userMessages
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error?.message || `HTTP ${response.status}`);
        }

        const data = await response.json();
        return data.content[0].text;
    }

    // DeepSeek API调用
    async callDeepSeekAPI(messages, apiKey, endpoint, model) {
        // DeepSeek API兼容OpenAI格式，但使用不同的端点
        const apiUrl = endpoint.includes('/v1') ? endpoint : `${endpoint}/chat/completions`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: model,
                messages: messages,
                temperature: 0.7,
                max_tokens: model === 'deepseek-reasoner' ? 32000 : 4000,
                stream: false
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error?.message || `HTTP ${response.status}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    // Gemini API调用
    async callGeminiAPI(messages, apiKey, endpoint, model) {
        // 过滤掉system消息，Gemini不支持system role
        const userMessages = messages.filter(msg => msg.role !== 'system');

        // 如果有system消息，将其内容添加到第一个用户消息前
        const systemMessage = messages.find(msg => msg.role === 'system');
        if (systemMessage && userMessages.length > 0) {
            userMessages[0].content = systemMessage.content + '\n\n' + userMessages[0].content;
        }

        // Gemini使用不同的消息格式
        const contents = userMessages.map(msg => ({
            role: msg.role === 'assistant' ? 'model' : 'user',
            parts: [{ text: msg.content }]
        }));

        const response = await fetch(`${endpoint}/models/${model}:generateContent`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-goog-api-key': apiKey
            },
            body: JSON.stringify({
                contents: contents,
                generationConfig: {
                    temperature: 0.7,
                    maxOutputTokens: 2000
                }
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error?.message || `HTTP ${response.status}`);
        }

        const data = await response.json();

        // 检查是否有候选回复
        if (!data.candidates || data.candidates.length === 0) {
            throw new Error('Gemini API返回了空的候选回复');
        }

        const candidate = data.candidates[0];
        if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
            throw new Error('Gemini API返回的内容格式不正确');
        }

        return candidate.content.parts[0].text;
    }

    // 通义千问API调用
    async callQwenAPI(messages, apiKey, endpoint, model) {
        const response = await fetch(`${endpoint}/services/aigc/text-generation/generation`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: model,
                input: {
                    messages: messages
                },
                parameters: {
                    temperature: 0.7,
                    max_tokens: 2000
                }
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || `HTTP ${response.status}`);
        }

        const data = await response.json();
        return data.output.text;
    }

    // 通用API调用
    async callGenericAPI(messages, apiKey, endpoint, model) {
        return await this.callOpenAIAPI(messages, apiKey, endpoint, model);
    }

    // 添加消息到聊天
    addMessage(role, content) {
        const message = {
            role: role,
            content: content,
            timestamp: new Date().toISOString()
        };

        this.messages.push(message);
        this.renderMessage(message);
        this.saveChatHistory();
        this.scrollToBottom();
    }

    // 渲染消息
    renderMessage(message) {
        // 移除欢迎消息
        const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        const messageElement = document.createElement('div');
        messageElement.className = `message ${message.role}`;

        const avatar = message.role === 'user' ?
            '<i class="fas fa-user"></i>' :
            '<i class="fas fa-robot"></i>';

        const time = new Date(message.timestamp).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageElement.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <div class="message-text">${this.formatMessageContent(message.content)}</div>
                <div class="message-time">${time}</div>
            </div>
        `;

        this.chatMessages.appendChild(messageElement);
    }

    // 格式化消息内容
    formatMessageContent(content) {
        // 简单的Markdown渲染
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/\n/g, '<br>');
    }

    // 滚动到底部
    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    // 开始新对话
    startNewChat() {
        if (this.messages.length > 0) {
            if (!confirm('确定要开始新对话吗？当前对话将被清空。')) {
                return;
            }
        }

        this.messages = [];
        this.chatMessages.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h2>欢迎使用AI聊天助手</h2>
                <p>您可以：</p>
                <ul>
                    <li>与AI进行智能对话</li>
                    <li>上传JSON文件作为上下文</li>
                    <li>配置不同的AI服务商</li>
                    <li>管理聊天历史记录</li>
                </ul>
                <p>开始对话吧！</p>
            </div>
        `;

        this.clearAttachedContext();
        this.saveChatHistory();
        this.showNotification('新对话', '已开始新的对话', 'info', 2000);
    }

    // 清空聊天历史
    clearChatHistory() {
        if (!confirm('确定要清空所有聊天历史吗？此操作不可撤销。')) {
            return;
        }

        this.startNewChat();
        localStorage.removeItem('aiChatHistory');
        this.showNotification('历史已清空', '所有聊天记录已删除', 'success');
    }

    // 导出聊天历史
    exportChatHistory() {
        if (this.messages.length === 0) {
            this.showNotification('无内容', '没有可导出的聊天记录', 'warning');
            return;
        }

        const exportData = {
            exportTime: new Date().toISOString(),
            messages: this.messages,
            jsonFiles: Array.from(this.jsonFiles.entries()).map(([name, info]) => ({
                name,
                size: info.size,
                uploadTime: info.uploadTime,
                content: info.content
            }))
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification('导出成功', '聊天历史已导出到文件', 'success');
    }

    // 保存聊天历史
    saveChatHistory() {
        try {
            const historyData = {
                messages: this.messages,
                lastUpdate: new Date().toISOString()
            };
            localStorage.setItem('aiChatHistory', JSON.stringify(historyData));
        } catch (error) {
            console.error('保存聊天历史失败:', error);
        }
    }

    // 加载聊天历史
    loadChatHistory() {
        try {
            const saved = localStorage.getItem('aiChatHistory');
            if (saved) {
                const historyData = JSON.parse(saved);
                this.messages = historyData.messages || [];

                if (this.messages.length > 0) {
                    // 清除欢迎消息
                    this.chatMessages.innerHTML = '';

                    // 渲染历史消息
                    this.messages.forEach(message => {
                        this.renderMessage(message);
                    });

                    this.scrollToBottom();
                }
            }
        } catch (error) {
            console.error('加载聊天历史失败:', error);
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new AIChat();
});
