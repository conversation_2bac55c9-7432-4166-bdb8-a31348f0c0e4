// AI聊天应用主脚本
class AIChat {
    constructor() {
        this.messages = [];
        this.jsonFiles = new Map();
        this.attachedContext = [];
        this.config = this.loadConfig();
        this.isTyping = false;

        // 自动上下文管理
        this.currentSessionId = this.generateSessionId();
        this.contextHistory = new Map(); // 存储每轮对话的上下文
        this.deletedContexts = new Map(); // 存储已删除的上下文，用于恢复
        this.autoContextSettings = {
            enabled: true,
            includeUserMessages: true,
            includeAIResponses: true,
            maxContextRounds: 50 // 最大保存轮数
        };

        this.initializeElements();
        this.bindEvents();
        this.loadChatHistory();
        this.loadContextHistory();
        this.updateAPIConfig();
        this.updateSessionDisplay();
    }
    
    // 初始化DOM元素引用
    initializeElements() {
        // 侧边栏元素
        this.sidebar = document.getElementById('sidebar');
        this.sidebarToggle = document.getElementById('sidebarToggle');
        
        // API配置元素
        this.apiProvider = document.getElementById('apiProvider');
        this.apiKey = document.getElementById('apiKey');
        this.apiEndpoint = document.getElementById('apiEndpoint');
        this.modelName = document.getElementById('modelName');
        this.saveConfigBtn = document.getElementById('saveConfig');
        this.toggleApiKeyBtn = document.getElementById('toggleApiKey');
        
        // JSON文件元素
        this.jsonFileInput = document.getElementById('jsonFileInput');
        this.uploadJsonBtn = document.getElementById('uploadJsonBtn');
        this.jsonFilesList = document.getElementById('jsonFilesList');
        
        // 聊天元素
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendMessageBtn = document.getElementById('sendMessageBtn');
        this.attachJsonBtn = document.getElementById('attachJsonBtn');
        this.newChatBtn = document.getElementById('newChatBtn');
        this.statusIndicator = document.getElementById('statusIndicator');
        
        // 上下文元素
        this.attachedContextElement = document.getElementById('attachedContext');
        this.contextFiles = document.getElementById('contextFiles');
        this.removeContext = document.getElementById('removeContext');
        
        // 自动上下文管理元素
        this.currentSessionName = document.getElementById('currentSessionName');
        this.contextCount = document.getElementById('contextCount');
        this.autoContextEnabled = document.getElementById('autoContextEnabled');
        this.includeUserMessages = document.getElementById('includeUserMessages');
        this.includeAIResponses = document.getElementById('includeAIResponses');
        this.viewContextBtn = document.getElementById('viewContextBtn');
        this.exportContextBtn = document.getElementById('exportContextBtn');
        this.cleanContextBtn = document.getElementById('cleanContextBtn');

        // 历史管理元素
        this.clearHistoryBtn = document.getElementById('clearHistory');
        this.exportHistoryBtn = document.getElementById('exportHistory');
        
        // 上下文管理器模态框元素
        this.contextManagerModal = document.getElementById('contextManagerModal');
        this.closeContextManager = document.getElementById('closeContextManager');
        this.contextList = document.getElementById('contextList');
        this.selectAllContextBtn = document.getElementById('selectAllContextBtn');
        this.deselectAllContextBtn = document.getElementById('deselectAllContextBtn');
        this.deleteSelectedContextBtn = document.getElementById('deleteSelectedContextBtn');
        this.restoreContextBtn = document.getElementById('restoreContextBtn');
        this.compactContextBtn = document.getElementById('compactContextBtn');
        this.exportSelectedContextBtn = document.getElementById('exportSelectedContextBtn');
        this.saveContextChangesBtn = document.getElementById('saveContextChangesBtn');
        this.cancelContextChangesBtn = document.getElementById('cancelContextChangesBtn');

        // JSON查看器模态框元素
        this.jsonViewerModal = document.getElementById('jsonViewerModal');
        this.closeJsonViewer = document.getElementById('closeJsonViewer');
        this.jsonViewerTitle = document.getElementById('jsonViewerTitle');
        this.jsonDisplay = document.getElementById('jsonDisplay');
        this.jsonEditor = document.getElementById('jsonEditor');
        this.editJsonBtn = document.getElementById('editJsonBtn');
        this.formatJsonBtn = document.getElementById('formatJsonBtn');
        this.validateJsonBtn = document.getElementById('validateJsonBtn');
        this.saveJsonBtn = document.getElementById('saveJsonBtn');
        this.cancelEditBtn = document.getElementById('cancelEditBtn');
        
        // 加载和通知元素
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.notificationContainer = document.getElementById('notificationContainer');
    }
    
    // 绑定事件监听器
    bindEvents() {
        // 侧边栏切换
        this.sidebarToggle?.addEventListener('click', () => this.toggleSidebar());
        
        // API配置事件
        this.apiProvider.addEventListener('change', () => this.updateAPIDefaults());
        this.saveConfigBtn.addEventListener('click', () => this.saveConfig());
        this.toggleApiKeyBtn.addEventListener('click', () => this.toggleApiKeyVisibility());
        
        // JSON文件事件
        this.uploadJsonBtn.addEventListener('click', () => this.jsonFileInput.click());
        this.jsonFileInput.addEventListener('change', (e) => this.handleJsonUpload(e));
        
        // 聊天事件
        this.sendMessageBtn.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keydown', (e) => this.handleInputKeydown(e));
        this.messageInput.addEventListener('input', () => this.adjustTextareaHeight());
        this.attachJsonBtn.addEventListener('click', () => this.showJsonAttachMenu());
        this.newChatBtn.addEventListener('click', () => this.startNewChat());
        
        // 自动上下文管理事件
        this.autoContextEnabled.addEventListener('change', () => this.updateAutoContextSettings());
        this.includeUserMessages.addEventListener('change', () => this.updateAutoContextSettings());
        this.includeAIResponses.addEventListener('change', () => this.updateAutoContextSettings());
        this.viewContextBtn.addEventListener('click', () => this.showContextManager());
        this.exportContextBtn.addEventListener('click', () => this.exportAllContexts());
        this.cleanContextBtn.addEventListener('click', () => this.cleanRedundantContexts());

        // 上下文事件
        this.removeContext.addEventListener('click', () => this.clearAttachedContext());
        
        // 历史管理事件
        this.clearHistoryBtn.addEventListener('click', () => this.clearChatHistory());
        this.exportHistoryBtn.addEventListener('click', () => this.exportChatHistory());
        
        // 上下文管理器事件
        this.closeContextManager.addEventListener('click', () => this.closeContextManagerModal());
        this.selectAllContextBtn.addEventListener('click', () => this.selectAllContexts());
        this.deselectAllContextBtn.addEventListener('click', () => this.deselectAllContexts());
        this.deleteSelectedContextBtn.addEventListener('click', () => this.deleteSelectedContexts());
        this.restoreContextBtn.addEventListener('click', () => this.restoreDeletedContexts());
        this.compactContextBtn.addEventListener('click', () => this.compactContexts());
        this.exportSelectedContextBtn.addEventListener('click', () => this.exportSelectedContexts());
        this.saveContextChangesBtn.addEventListener('click', () => this.saveContextChanges());
        this.cancelContextChangesBtn.addEventListener('click', () => this.cancelContextChanges());

        // JSON查看器模态框事件
        this.closeJsonViewer.addEventListener('click', () => this.closeJsonViewerModal());
        this.editJsonBtn.addEventListener('click', () => this.toggleJsonEdit());
        this.formatJsonBtn.addEventListener('click', () => this.formatJson());
        this.validateJsonBtn.addEventListener('click', () => this.validateJson());
        this.saveJsonBtn.addEventListener('click', () => this.saveJsonEdit());
        this.cancelEditBtn.addEventListener('click', () => this.cancelJsonEdit());
        
        // 模态框外部点击关闭
        this.contextManagerModal.addEventListener('click', (e) => {
            if (e.target === this.contextManagerModal) {
                this.closeContextManagerModal();
            }
        });

        this.jsonViewerModal.addEventListener('click', (e) => {
            if (e.target === this.jsonViewerModal) {
                this.closeJsonViewerModal();
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleGlobalKeydown(e));
        
        // 窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
    }
    
    // 加载配置
    loadConfig() {
        const defaultConfig = {
            apiProvider: 'openai',
            apiKey: '',
            apiEndpoint: 'https://api.openai.com/v1',
            modelName: 'gpt-3.5-turbo'
        };
        
        try {
            const saved = localStorage.getItem('aiChatConfig');
            return saved ? { ...defaultConfig, ...JSON.parse(saved) } : defaultConfig;
        } catch (error) {
            console.error('加载配置失败:', error);
            return defaultConfig;
        }
    }
    
    // 保存配置
    saveConfig() {
        const config = {
            apiProvider: this.apiProvider.value,
            apiKey: this.apiKey.value,
            apiEndpoint: this.apiEndpoint.value,
            modelName: this.modelName.value
        };
        
        try {
            localStorage.setItem('aiChatConfig', JSON.stringify(config));
            this.config = config;
            this.showNotification('配置已保存', '设置已成功保存到本地存储', 'success');
            this.updateStatus('已配置', 'ready');
        } catch (error) {
            console.error('保存配置失败:', error);
            this.showNotification('保存失败', '无法保存配置到本地存储', 'error');
        }
    }
    
    // 更新API默认值
    updateAPIDefaults() {
        const provider = this.apiProvider.value;
        const defaults = {
            openai: {
                endpoint: 'https://api.openai.com/v1',
                model: 'gpt-3.5-turbo'
            },
            claude: {
                endpoint: 'https://api.anthropic.com/v1',
                model: 'claude-3-sonnet-20240229'
            },
            deepseek: {
                endpoint: 'https://api.deepseek.com',
                model: 'deepseek-chat'
            },
            'deepseek-r1': {
                endpoint: 'https://api.deepseek.com',
                model: 'deepseek-reasoner'
            },
            gemini: {
                endpoint: 'https://generativelanguage.googleapis.com/v1beta',
                model: 'gemini-pro'
            },
            'gemini-2-flash': {
                endpoint: 'https://generativelanguage.googleapis.com/v1beta',
                model: 'gemini-2.0-flash'
            },
            qwen: {
                endpoint: 'https://dashscope.aliyuncs.com/api/v1',
                model: 'qwen-turbo'
            },
            custom: {
                endpoint: '',
                model: ''
            }
        };

        const config = defaults[provider];
        if (config) {
            this.apiEndpoint.value = config.endpoint;
            this.modelName.value = config.model;
        }
    }
    
    // 更新API配置显示
    updateAPIConfig() {
        this.apiProvider.value = this.config.apiProvider;
        this.apiKey.value = this.config.apiKey;
        this.apiEndpoint.value = this.config.apiEndpoint;
        this.modelName.value = this.config.modelName;
    }
    
    // 切换API密钥可见性
    toggleApiKeyVisibility() {
        const isPassword = this.apiKey.type === 'password';
        this.apiKey.type = isPassword ? 'text' : 'password';
        this.toggleApiKeyBtn.innerHTML = isPassword ? 
            '<i class="fas fa-eye-slash"></i>' : 
            '<i class="fas fa-eye"></i>';
    }
    
    // 切换侧边栏
    toggleSidebar() {
        this.sidebar.classList.toggle('open');
    }
    
    // 处理窗口大小变化
    handleResize() {
        if (window.innerWidth > 768) {
            this.sidebar.classList.remove('open');
        }
    }
    
    // 调整文本框高度
    adjustTextareaHeight() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }
    
    // 处理输入框键盘事件
    handleInputKeydown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
    }
    
    // 处理全局键盘事件
    handleGlobalKeydown(e) {
        // Ctrl/Cmd + K 聚焦到输入框
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.messageInput.focus();
        }
        
        // Escape 关闭模态框
        if (e.key === 'Escape') {
            this.closeJsonViewerModal();
        }
    }
    
    // 更新状态指示器
    updateStatus(text, type = 'ready') {
        const statusText = this.statusIndicator.querySelector('.status-text');
        const statusDot = this.statusIndicator.querySelector('.status-dot');

        statusText.textContent = text;
        statusDot.className = `status-dot ${type}`;
    }

    // 生成会话ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 更新会话显示
    updateSessionDisplay() {
        const sessionName = this.currentSessionId.replace('session_', '').substring(0, 8);
        this.currentSessionName.textContent = `会话-${sessionName}`;
        this.contextCount.textContent = `${this.contextHistory.size}轮上下文`;
    }

    // 更新自动上下文设置
    updateAutoContextSettings() {
        this.autoContextSettings = {
            enabled: this.autoContextEnabled.checked,
            includeUserMessages: this.includeUserMessages.checked,
            includeAIResponses: this.includeAIResponses.checked,
            maxContextRounds: 50
        };

        this.saveAutoContextSettings();
        this.showNotification('设置已更新', '自动上下文设置已保存', 'success', 2000);
    }

    // 保存自动上下文设置
    saveAutoContextSettings() {
        try {
            localStorage.setItem('autoContextSettings', JSON.stringify(this.autoContextSettings));
        } catch (error) {
            console.error('保存自动上下文设置失败:', error);
        }
    }

    // 加载自动上下文设置
    loadAutoContextSettings() {
        try {
            const saved = localStorage.getItem('autoContextSettings');
            if (saved) {
                this.autoContextSettings = { ...this.autoContextSettings, ...JSON.parse(saved) };

                // 更新UI
                this.autoContextEnabled.checked = this.autoContextSettings.enabled;
                this.includeUserMessages.checked = this.autoContextSettings.includeUserMessages;
                this.includeAIResponses.checked = this.autoContextSettings.includeAIResponses;
            }
        } catch (error) {
            console.error('加载自动上下文设置失败:', error);
        }
    }

    // 生成上下文JSON
    generateContextJSON(userMessage, aiResponse, roundNumber) {
        const context = {
            sessionId: this.currentSessionId,
            roundNumber: roundNumber,
            timestamp: new Date().toISOString(),
            conversation: {}
        };

        if (this.autoContextSettings.includeUserMessages && userMessage) {
            context.conversation.user = {
                message: userMessage,
                timestamp: new Date().toISOString(),
                role: 'user'
            };
        }

        if (this.autoContextSettings.includeAIResponses && aiResponse) {
            context.conversation.assistant = {
                message: aiResponse,
                timestamp: new Date().toISOString(),
                role: 'assistant'
            };
        }

        // 添加元数据
        context.metadata = {
            messageCount: this.messages.length,
            sessionDuration: Date.now() - parseInt(this.currentSessionId.split('_')[1]),
            apiProvider: this.config.apiProvider,
            modelName: this.config.modelName
        };

        // 添加附加的外部JSON上下文
        if (this.attachedContext.length > 0) {
            context.externalContext = {};
            this.attachedContext.forEach(fileName => {
                const fileInfo = this.jsonFiles.get(fileName);
                if (fileInfo) {
                    context.externalContext[fileName] = fileInfo.content;
                }
            });
        }

        return context;
    }

    // 保存上下文到历史
    saveContextToHistory(contextJSON, roundNumber) {
        if (!this.autoContextSettings.enabled) return;

        const contextKey = `round_${roundNumber}`;
        this.contextHistory.set(contextKey, contextJSON);

        // 限制最大保存轮数
        if (this.contextHistory.size > this.autoContextSettings.maxContextRounds) {
            const oldestKey = Array.from(this.contextHistory.keys())[0];
            this.contextHistory.delete(oldestKey);
        }

        this.saveContextHistory();
        this.updateSessionDisplay();
    }

    // 保存上下文历史到本地存储
    saveContextHistory() {
        try {
            const contextData = {
                sessionId: this.currentSessionId,
                contexts: Array.from(this.contextHistory.entries()),
                lastUpdate: new Date().toISOString()
            };
            localStorage.setItem(`contextHistory_${this.currentSessionId}`, JSON.stringify(contextData));
        } catch (error) {
            console.error('保存上下文历史失败:', error);
        }
    }

    // 加载上下文历史
    loadContextHistory() {
        try {
            const saved = localStorage.getItem(`contextHistory_${this.currentSessionId}`);
            if (saved) {
                const contextData = JSON.parse(saved);
                this.contextHistory = new Map(contextData.contexts || []);
                this.updateSessionDisplay();
            }
        } catch (error) {
            console.error('加载上下文历史失败:', error);
        }

        this.loadAutoContextSettings();
    }

    // 显示通知
    showNotification(title, message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const iconMap = {
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle',
            info: 'fas fa-info-circle'
        };

        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${iconMap[type]}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });

        this.notificationContainer.appendChild(notification);

        // 自动移除通知
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }
    }

    // 显示加载状态
    showLoading(show = true) {
        this.loadingOverlay.style.display = show ? 'flex' : 'none';
    }

    // JSON文件上传处理
    async handleJsonUpload(event) {
        const files = Array.from(event.target.files);

        for (const file of files) {
            if (!file.name.toLowerCase().endsWith('.json')) {
                this.showNotification('文件格式错误', `${file.name} 不是有效的JSON文件`, 'warning');
                continue;
            }

            try {
                const content = await this.readFileAsText(file);
                const jsonData = JSON.parse(content);

                const fileInfo = {
                    name: file.name,
                    size: file.size,
                    content: jsonData,
                    uploadTime: new Date().toISOString()
                };

                this.jsonFiles.set(file.name, fileInfo);
                this.updateJsonFilesList();
                this.showNotification('上传成功', `${file.name} 已成功上传`, 'success');

            } catch (error) {
                console.error('JSON文件解析失败:', error);
                this.showNotification('解析失败', `${file.name} 不是有效的JSON格式`, 'error');
            }
        }

        // 清空文件输入
        event.target.value = '';
    }

    // 读取文件内容
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    }

    // 更新JSON文件列表显示
    updateJsonFilesList() {
        this.jsonFilesList.innerHTML = '';

        if (this.jsonFiles.size === 0) {
            this.jsonFilesList.innerHTML = '<p style="color: var(--text-muted); font-size: 0.875rem; text-align: center; padding: 1rem;">暂无JSON文件</p>';
            return;
        }

        this.jsonFiles.forEach((fileInfo, fileName) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'json-file-item';

            fileItem.innerHTML = `
                <div class="json-file-info">
                    <div class="json-file-name">${fileName}</div>
                    <div class="json-file-size">${this.formatFileSize(fileInfo.size)}</div>
                </div>
                <div class="json-file-actions">
                    <button class="btn-icon view-json" title="查看">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon attach-json" title="附加到对话">
                        <i class="fas fa-paperclip"></i>
                    </button>
                    <button class="btn-icon delete-json" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            // 绑定事件
            fileItem.querySelector('.view-json').addEventListener('click', () => {
                this.viewJsonFile(fileName);
            });

            fileItem.querySelector('.attach-json').addEventListener('click', () => {
                this.attachJsonToContext(fileName);
            });

            fileItem.querySelector('.delete-json').addEventListener('click', () => {
                this.deleteJsonFile(fileName);
            });

            this.jsonFilesList.appendChild(fileItem);
        });
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 查看JSON文件
    viewJsonFile(fileName) {
        const fileInfo = this.jsonFiles.get(fileName);
        if (!fileInfo) return;

        this.currentEditingFile = fileName;
        this.jsonViewerTitle.textContent = `JSON查看器 - ${fileName}`;
        this.jsonDisplay.textContent = JSON.stringify(fileInfo.content, null, 2);
        this.jsonEditor.value = JSON.stringify(fileInfo.content, null, 2);

        // 重置编辑状态
        this.jsonDisplay.style.display = 'block';
        this.jsonEditor.style.display = 'none';
        this.editJsonBtn.innerHTML = '<i class="fas fa-edit"></i> 编辑';
        this.saveJsonBtn.style.display = 'none';
        this.cancelEditBtn.style.display = 'none';

        this.showJsonViewerModal();
    }

    // 显示JSON查看器模态框
    showJsonViewerModal() {
        this.jsonViewerModal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    // 关闭JSON查看器模态框
    closeJsonViewerModal() {
        this.jsonViewerModal.classList.remove('show');
        document.body.style.overflow = '';
        this.currentEditingFile = null;
    }

    // 切换JSON编辑模式
    toggleJsonEdit() {
        const isEditing = this.jsonEditor.style.display !== 'none';

        if (isEditing) {
            // 退出编辑模式
            this.jsonDisplay.style.display = 'block';
            this.jsonEditor.style.display = 'none';
            this.editJsonBtn.innerHTML = '<i class="fas fa-edit"></i> 编辑';
            this.saveJsonBtn.style.display = 'none';
            this.cancelEditBtn.style.display = 'none';
        } else {
            // 进入编辑模式
            this.jsonDisplay.style.display = 'none';
            this.jsonEditor.style.display = 'block';
            this.editJsonBtn.innerHTML = '<i class="fas fa-eye"></i> 预览';
            this.saveJsonBtn.style.display = 'inline-flex';
            this.cancelEditBtn.style.display = 'inline-flex';
            this.jsonEditor.focus();
        }
    }

    // 格式化JSON
    formatJson() {
        try {
            const isEditing = this.jsonEditor.style.display !== 'none';
            if (isEditing) {
                const content = JSON.parse(this.jsonEditor.value);
                this.jsonEditor.value = JSON.stringify(content, null, 2);
            } else {
                const content = JSON.parse(this.jsonDisplay.textContent);
                this.jsonDisplay.textContent = JSON.stringify(content, null, 2);
            }
            this.showNotification('格式化成功', 'JSON已格式化', 'success', 2000);
        } catch (error) {
            this.showNotification('格式化失败', 'JSON格式不正确', 'error');
        }
    }

    // 验证JSON
    validateJson() {
        try {
            const isEditing = this.jsonEditor.style.display !== 'none';
            const content = isEditing ? this.jsonEditor.value : this.jsonDisplay.textContent;
            JSON.parse(content);
            this.showNotification('验证成功', 'JSON格式正确', 'success', 2000);
        } catch (error) {
            this.showNotification('验证失败', `JSON格式错误: ${error.message}`, 'error');
        }
    }

    // 保存JSON编辑
    saveJsonEdit() {
        try {
            const content = JSON.parse(this.jsonEditor.value);
            const fileInfo = this.jsonFiles.get(this.currentEditingFile);

            if (fileInfo) {
                fileInfo.content = content;
                this.jsonFiles.set(this.currentEditingFile, fileInfo);
                this.jsonDisplay.textContent = JSON.stringify(content, null, 2);
                this.toggleJsonEdit(); // 退出编辑模式
                this.showNotification('保存成功', 'JSON文件已更新', 'success');
            }
        } catch (error) {
            this.showNotification('保存失败', `JSON格式错误: ${error.message}`, 'error');
        }
    }

    // 取消JSON编辑
    cancelJsonEdit() {
        const fileInfo = this.jsonFiles.get(this.currentEditingFile);
        if (fileInfo) {
            this.jsonEditor.value = JSON.stringify(fileInfo.content, null, 2);
        }
        this.toggleJsonEdit(); // 退出编辑模式
    }

    // 删除JSON文件
    deleteJsonFile(fileName) {
        if (confirm(`确定要删除 ${fileName} 吗？`)) {
            this.jsonFiles.delete(fileName);
            this.updateJsonFilesList();

            // 如果该文件在上下文中，也要移除
            this.attachedContext = this.attachedContext.filter(name => name !== fileName);
            this.updateAttachedContextDisplay();

            this.showNotification('删除成功', `${fileName} 已删除`, 'success');
        }
    }

    // 附加JSON到上下文
    attachJsonToContext(fileName) {
        if (!this.attachedContext.includes(fileName)) {
            this.attachedContext.push(fileName);
            this.updateAttachedContextDisplay();
            this.showNotification('附加成功', `${fileName} 已添加到对话上下文`, 'success', 2000);
        } else {
            this.showNotification('已存在', `${fileName} 已在上下文中`, 'warning', 2000);
        }
    }

    // 更新附加上下文显示
    updateAttachedContextDisplay() {
        if (this.attachedContext.length === 0) {
            this.attachedContextElement.style.display = 'none';
        } else {
            this.attachedContextElement.style.display = 'flex';
            this.contextFiles.textContent = this.attachedContext.join(', ');
        }
    }

    // 清除附加上下文
    clearAttachedContext() {
        this.attachedContext = [];
        this.updateAttachedContextDisplay();
        this.showNotification('已清除', '上下文已清空', 'info', 2000);
    }

    // 显示JSON附加菜单
    showJsonAttachMenu() {
        if (this.jsonFiles.size === 0) {
            this.showNotification('无可用文件', '请先上传JSON文件', 'warning');
            return;
        }

        // 创建简单的选择菜单
        const fileNames = Array.from(this.jsonFiles.keys());
        const availableFiles = fileNames.filter(name => !this.attachedContext.includes(name));

        if (availableFiles.length === 0) {
            this.showNotification('无可附加文件', '所有JSON文件都已在上下文中', 'info');
            return;
        }

        // 简单实现：附加第一个可用文件
        this.attachJsonToContext(availableFiles[0]);
    }

    // 发送消息
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isTyping) return;

        // 检查API配置
        if (!this.config.apiKey) {
            this.showNotification('配置错误', '请先配置API密钥', 'warning');
            return;
        }

        // 添加用户消息
        this.addMessage('user', message);
        this.messageInput.value = '';
        this.adjustTextareaHeight();

        // 准备上下文数据
        const contextData = this.prepareContextData();

        try {
            this.isTyping = true;
            this.updateStatus('AI正在思考...', 'connecting');
            this.showLoading(true);

            // 调用AI API
            const response = await this.callAIAPI(message, contextData);

            // 添加AI回复
            this.addMessage('assistant', response);
            this.updateStatus('就绪', 'ready');

            // 自动生成并保存上下文JSON
            if (this.autoContextSettings.enabled) {
                const roundNumber = Math.ceil(this.messages.length / 2);
                const contextJSON = this.generateContextJSON(message, response, roundNumber);
                this.saveContextToHistory(contextJSON, roundNumber);
            }

        } catch (error) {
            console.error('AI API调用失败:', error);
            this.addMessage('assistant', '抱歉，我遇到了一些问题。请检查网络连接和API配置。');
            this.updateStatus('错误', 'error');
            this.showNotification('请求失败', error.message, 'error');
        } finally {
            this.isTyping = false;
            this.showLoading(false);
        }
    }

    // 准备上下文数据
    prepareContextData() {
        const contextData = {};

        this.attachedContext.forEach(fileName => {
            const fileInfo = this.jsonFiles.get(fileName);
            if (fileInfo) {
                contextData[fileName] = fileInfo.content;
            }
        });

        return contextData;
    }

    // 调用AI API
    async callAIAPI(message, contextData) {
        const { apiProvider, apiKey, apiEndpoint, modelName } = this.config;

        // 构建消息历史
        const messages = this.buildMessageHistory(message, contextData);

        // 根据不同的API提供商构建请求
        switch (apiProvider) {
            case 'openai':
                return await this.callOpenAIAPI(messages, apiKey, apiEndpoint, modelName);
            case 'claude':
                return await this.callClaudeAPI(messages, apiKey, apiEndpoint, modelName);
            case 'deepseek':
            case 'deepseek-r1':
                return await this.callDeepSeekAPI(messages, apiKey, apiEndpoint, modelName);
            case 'gemini':
            case 'gemini-2-flash':
                return await this.callGeminiAPI(messages, apiKey, apiEndpoint, modelName);
            case 'qwen':
                return await this.callQwenAPI(messages, apiKey, apiEndpoint, modelName);
            default:
                return await this.callGenericAPI(messages, apiKey, apiEndpoint, modelName);
        }
    }

    // 构建消息历史
    buildMessageHistory(currentMessage, contextData) {
        const messages = [];

        // 添加系统提示（如果有上下文数据）
        if (Object.keys(contextData).length > 0) {
            const contextPrompt = `以下是用户提供的JSON上下文数据，请在回答时参考这些信息：\n\n${JSON.stringify(contextData, null, 2)}`;
            messages.push({
                role: 'system',
                content: contextPrompt
            });
        }

        // 添加历史消息（最近10条）
        const recentMessages = this.messages.slice(-10);
        recentMessages.forEach(msg => {
            messages.push({
                role: msg.role,
                content: msg.content
            });
        });

        // 添加当前消息
        messages.push({
            role: 'user',
            content: currentMessage
        });

        return messages;
    }

    // OpenAI API调用
    async callOpenAIAPI(messages, apiKey, endpoint, model) {
        const response = await fetch(`${endpoint}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: model,
                messages: messages,
                temperature: 0.7,
                max_tokens: 2000
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error?.message || `HTTP ${response.status}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    // Claude API调用
    async callClaudeAPI(messages, apiKey, endpoint, model) {
        // 将messages格式转换为Claude格式
        const systemMessage = messages.find(m => m.role === 'system');
        const userMessages = messages.filter(m => m.role !== 'system');

        const response = await fetch(`${endpoint}/messages`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': apiKey,
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: model,
                max_tokens: 2000,
                system: systemMessage?.content || '',
                messages: userMessages
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error?.message || `HTTP ${response.status}`);
        }

        const data = await response.json();
        return data.content[0].text;
    }

    // DeepSeek API调用
    async callDeepSeekAPI(messages, apiKey, endpoint, model) {
        // DeepSeek API兼容OpenAI格式，但使用不同的端点
        const apiUrl = endpoint.includes('/v1') ? endpoint : `${endpoint}/chat/completions`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: model,
                messages: messages,
                temperature: 0.7,
                max_tokens: model === 'deepseek-reasoner' ? 32000 : 4000,
                stream: false
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error?.message || `HTTP ${response.status}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    // Gemini API调用
    async callGeminiAPI(messages, apiKey, endpoint, model) {
        // 过滤掉system消息，Gemini不支持system role
        const userMessages = messages.filter(msg => msg.role !== 'system');

        // 如果有system消息，将其内容添加到第一个用户消息前
        const systemMessage = messages.find(msg => msg.role === 'system');
        if (systemMessage && userMessages.length > 0) {
            userMessages[0].content = systemMessage.content + '\n\n' + userMessages[0].content;
        }

        // Gemini使用不同的消息格式
        const contents = userMessages.map(msg => ({
            role: msg.role === 'assistant' ? 'model' : 'user',
            parts: [{ text: msg.content }]
        }));

        const response = await fetch(`${endpoint}/models/${model}:generateContent`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-goog-api-key': apiKey
            },
            body: JSON.stringify({
                contents: contents,
                generationConfig: {
                    temperature: 0.7,
                    maxOutputTokens: 2000
                }
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error?.message || `HTTP ${response.status}`);
        }

        const data = await response.json();

        // 检查是否有候选回复
        if (!data.candidates || data.candidates.length === 0) {
            throw new Error('Gemini API返回了空的候选回复');
        }

        const candidate = data.candidates[0];
        if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
            throw new Error('Gemini API返回的内容格式不正确');
        }

        return candidate.content.parts[0].text;
    }

    // 通义千问API调用
    async callQwenAPI(messages, apiKey, endpoint, model) {
        const response = await fetch(`${endpoint}/services/aigc/text-generation/generation`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: model,
                input: {
                    messages: messages
                },
                parameters: {
                    temperature: 0.7,
                    max_tokens: 2000
                }
            })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || `HTTP ${response.status}`);
        }

        const data = await response.json();
        return data.output.text;
    }

    // 通用API调用
    async callGenericAPI(messages, apiKey, endpoint, model) {
        return await this.callOpenAIAPI(messages, apiKey, endpoint, model);
    }

    // 添加消息到聊天
    addMessage(role, content) {
        const message = {
            role: role,
            content: content,
            timestamp: new Date().toISOString()
        };

        this.messages.push(message);
        this.renderMessage(message);
        this.saveChatHistory();
        this.scrollToBottom();
    }

    // 渲染消息
    renderMessage(message) {
        // 移除欢迎消息
        const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        const messageElement = document.createElement('div');
        messageElement.className = `message ${message.role}`;

        const avatar = message.role === 'user' ?
            '<i class="fas fa-user"></i>' :
            '<i class="fas fa-robot"></i>';

        const time = new Date(message.timestamp).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageElement.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <div class="message-text">${this.formatMessageContent(message.content)}</div>
                <div class="message-time">${time}</div>
            </div>
        `;

        this.chatMessages.appendChild(messageElement);
    }

    // 格式化消息内容
    formatMessageContent(content) {
        // 简单的Markdown渲染
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/\n/g, '<br>');
    }

    // 滚动到底部
    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    // 开始新对话
    startNewChat() {
        if (this.messages.length > 0 || this.contextHistory.size > 0) {
            if (!confirm('确定要开始新对话吗？当前对话和上下文将被保存并创建新会话。')) {
                return;
            }
        }

        // 保存当前会话的最终状态
        this.saveContextHistory();
        this.saveChatHistory();

        // 创建新会话
        this.currentSessionId = this.generateSessionId();
        this.messages = [];
        this.contextHistory.clear();
        this.deletedContexts.clear();

        // 重置界面
        this.chatMessages.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h2>欢迎使用AI聊天助手</h2>
                <p>您可以：</p>
                <ul>
                    <li>与AI进行智能对话</li>
                    <li>自动生成上下文JSON文件</li>
                    <li>管理每轮对话的上下文</li>
                    <li>配置不同的AI服务商</li>
                </ul>
                <p>开始新的智能对话吧！</p>
            </div>
        `;

        this.clearAttachedContext();
        this.updateSessionDisplay();
        this.showNotification('新会话已创建', `会话ID: ${this.currentSessionId.substring(8, 16)}`, 'success', 3000);
    }

    // 清空聊天历史
    clearChatHistory() {
        if (!confirm('确定要清空所有聊天历史吗？此操作不可撤销。')) {
            return;
        }

        this.startNewChat();
        localStorage.removeItem('aiChatHistory');
        this.showNotification('历史已清空', '所有聊天记录已删除', 'success');
    }

    // 导出聊天历史
    exportChatHistory() {
        if (this.messages.length === 0) {
            this.showNotification('无内容', '没有可导出的聊天记录', 'warning');
            return;
        }

        const exportData = {
            exportTime: new Date().toISOString(),
            messages: this.messages,
            jsonFiles: Array.from(this.jsonFiles.entries()).map(([name, info]) => ({
                name,
                size: info.size,
                uploadTime: info.uploadTime,
                content: info.content
            }))
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification('导出成功', '聊天历史已导出到文件', 'success');
    }

    // 保存聊天历史
    saveChatHistory() {
        try {
            const historyData = {
                messages: this.messages,
                lastUpdate: new Date().toISOString()
            };
            localStorage.setItem('aiChatHistory', JSON.stringify(historyData));
        } catch (error) {
            console.error('保存聊天历史失败:', error);
        }
    }

    // 加载聊天历史
    loadChatHistory() {
        try {
            const saved = localStorage.getItem('aiChatHistory');
            if (saved) {
                const historyData = JSON.parse(saved);
                this.messages = historyData.messages || [];

                if (this.messages.length > 0) {
                    // 清除欢迎消息
                    this.chatMessages.innerHTML = '';

                    // 渲染历史消息
                    this.messages.forEach(message => {
                        this.renderMessage(message);
                    });

                    this.scrollToBottom();
                }
            }
        } catch (error) {
            console.error('加载聊天历史失败:', error);
        }
    }

    // 显示上下文管理器
    showContextManager() {
        this.renderContextList();
        this.contextManagerModal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    // 关闭上下文管理器
    closeContextManagerModal() {
        this.contextManagerModal.classList.remove('show');
        document.body.style.overflow = '';
    }

    // 渲染上下文列表
    renderContextList() {
        const contextArray = Array.from(this.contextHistory.entries()).sort((a, b) => {
            const aNum = parseInt(a[0].split('_')[1]);
            const bNum = parseInt(b[0].split('_')[1]);
            return aNum - bNum;
        });

        let html = '';

        if (contextArray.length === 0) {
            html = `
                <div class="empty-context">
                    <div class="empty-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>暂无上下文数据</h3>
                    <p>开始对话后将自动生成上下文JSON文件</p>
                </div>
            `;
        } else {
            // 添加统计信息
            const totalSize = contextArray.reduce((sum, [key, context]) => {
                return sum + JSON.stringify(context).length;
            }, 0);

            html += `
                <div class="context-stats">
                    <div class="stat-item">
                        <div class="stat-value">${contextArray.length}</div>
                        <div class="stat-label">总轮数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${this.formatFileSize(totalSize)}</div>
                        <div class="stat-label">总大小</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${this.deletedContexts.size}</div>
                        <div class="stat-label">已删除</div>
                    </div>
                </div>
            `;

            // 渲染每个上下文项
            contextArray.forEach(([key, context]) => {
                const isDeleted = this.deletedContexts.has(key);
                const contextSize = JSON.stringify(context).length;
                const preview = this.generateContextPreview(context);

                html += `
                    <div class="context-item ${isDeleted ? 'deleted' : ''}" data-key="${key}">
                        <div class="context-checkbox">
                            <input type="checkbox" id="context_${key}" ${isDeleted ? 'disabled' : ''}>
                        </div>
                        <div class="context-content">
                            <div class="context-header">
                                <div class="context-meta">
                                    <span class="context-round">第${context.roundNumber}轮</span>
                                    <span class="context-timestamp">${new Date(context.timestamp).toLocaleString('zh-CN')}</span>
                                    <span class="context-size">${this.formatFileSize(contextSize)}</span>
                                </div>
                            </div>
                            <div class="context-preview">${preview}</div>
                        </div>
                        <div class="context-actions">
                            <button class="btn-icon view-context" title="查看详情" data-key="${key}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-icon edit-context" title="编辑" data-key="${key}" ${isDeleted ? 'disabled' : ''}>
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon ${isDeleted ? 'restore-context' : 'delete-context'}"
                                    title="${isDeleted ? '恢复' : '删除'}" data-key="${key}">
                                <i class="fas fa-${isDeleted ? 'undo' : 'trash'}"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
        }

        this.contextList.innerHTML = html;
        this.bindContextListEvents();
    }

    // 生成上下文预览
    generateContextPreview(context) {
        const preview = {
            会话ID: context.sessionId.substring(8, 16),
            轮次: context.roundNumber,
            时间: new Date(context.timestamp).toLocaleString('zh-CN')
        };

        if (context.conversation) {
            if (context.conversation.user) {
                preview.用户消息 = context.conversation.user.message.substring(0, 100) +
                    (context.conversation.user.message.length > 100 ? '...' : '');
            }
            if (context.conversation.assistant) {
                preview.AI回复 = context.conversation.assistant.message.substring(0, 100) +
                    (context.conversation.assistant.message.length > 100 ? '...' : '');
            }
        }

        if (context.externalContext) {
            preview.外部上下文 = Object.keys(context.externalContext).join(', ');
        }

        return JSON.stringify(preview, null, 2);
    }

    // 绑定上下文列表事件
    bindContextListEvents() {
        // 查看上下文详情
        this.contextList.querySelectorAll('.view-context').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const key = e.target.closest('.view-context').dataset.key;
                this.viewContextDetails(key);
            });
        });

        // 编辑上下文
        this.contextList.querySelectorAll('.edit-context').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const key = e.target.closest('.edit-context').dataset.key;
                this.editContext(key);
            });
        });

        // 删除/恢复上下文
        this.contextList.querySelectorAll('.delete-context, .restore-context').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const key = e.target.closest('button').dataset.key;
                if (btn.classList.contains('delete-context')) {
                    this.deleteContext(key);
                } else {
                    this.restoreContext(key);
                }
            });
        });

        // 复选框事件
        this.contextList.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateContextSelection();
            });
        });
    }
}

    // 查看上下文详情
    viewContextDetails(key) {
        const context = this.contextHistory.get(key);
        if (!context) return;

        this.currentEditingFile = `context_${key}`;
        this.jsonViewerTitle.textContent = `上下文详情 - 第${context.roundNumber}轮`;
        this.jsonDisplay.textContent = JSON.stringify(context, null, 2);
        this.jsonEditor.value = JSON.stringify(context, null, 2);

        // 重置编辑状态
        this.jsonDisplay.style.display = 'block';
        this.jsonEditor.style.display = 'none';
        this.editJsonBtn.innerHTML = '<i class="fas fa-edit"></i> 编辑';
        this.saveJsonBtn.style.display = 'none';
        this.cancelEditBtn.style.display = 'none';

        this.showJsonViewerModal();
    }

    // 编辑上下文
    editContext(key) {
        this.viewContextDetails(key);
        // 自动进入编辑模式
        setTimeout(() => {
            this.toggleJsonEdit();
        }, 100);
    }

    // 删除上下文
    deleteContext(key) {
        const context = this.contextHistory.get(key);
        if (context) {
            this.deletedContexts.set(key, context);
            this.renderContextList();
            this.showNotification('已删除', `第${context.roundNumber}轮上下文已删除`, 'warning', 2000);
        }
    }

    // 恢复上下文
    restoreContext(key) {
        const context = this.deletedContexts.get(key);
        if (context) {
            this.deletedContexts.delete(key);
            this.renderContextList();
            this.showNotification('已恢复', `第${context.roundNumber}轮上下文已恢复`, 'success', 2000);
        }
    }

    // 全选上下文
    selectAllContexts() {
        this.contextList.querySelectorAll('input[type="checkbox"]:not(:disabled)').forEach(checkbox => {
            checkbox.checked = true;
        });
        this.updateContextSelection();
    }

    // 取消全选
    deselectAllContexts() {
        this.contextList.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = false;
        });
        this.updateContextSelection();
    }

    // 删除选中的上下文
    deleteSelectedContexts() {
        const selected = this.getSelectedContexts();
        if (selected.length === 0) {
            this.showNotification('未选择', '请先选择要删除的上下文', 'warning');
            return;
        }

        if (!confirm(`确定要删除选中的 ${selected.length} 个上下文吗？`)) {
            return;
        }

        selected.forEach(key => {
            this.deleteContext(key);
        });

        this.showNotification('批量删除完成', `已删除 ${selected.length} 个上下文`, 'success');
    }

    // 恢复已删除的上下文
    restoreDeletedContexts() {
        const deletedCount = this.deletedContexts.size;
        if (deletedCount === 0) {
            this.showNotification('无可恢复项', '没有已删除的上下文', 'info');
            return;
        }

        if (!confirm(`确定要恢复所有 ${deletedCount} 个已删除的上下文吗？`)) {
            return;
        }

        this.deletedContexts.clear();
        this.renderContextList();
        this.showNotification('恢复完成', `已恢复 ${deletedCount} 个上下文`, 'success');
    }

    // 压缩冗余上下文
    compactContexts() {
        // 这里可以实现智能压缩逻辑，比如合并相似内容、删除重复信息等
        this.showNotification('功能开发中', '智能压缩功能正在开发中', 'info');
    }

    // 导出选中的上下文
    exportSelectedContexts() {
        const selected = this.getSelectedContexts();
        if (selected.length === 0) {
            this.showNotification('未选择', '请先选择要导出的上下文', 'warning');
            return;
        }

        const exportData = {
            sessionId: this.currentSessionId,
            exportTime: new Date().toISOString(),
            selectedContexts: selected.map(key => ({
                key: key,
                context: this.contextHistory.get(key)
            }))
        };

        this.downloadJSON(exportData, `selected_contexts_${this.currentSessionId.substring(8, 16)}.json`);
        this.showNotification('导出完成', `已导出 ${selected.length} 个上下文`, 'success');
    }

    // 导出所有上下文
    exportAllContexts() {
        if (this.contextHistory.size === 0) {
            this.showNotification('无内容', '没有可导出的上下文', 'warning');
            return;
        }

        const exportData = {
            sessionId: this.currentSessionId,
            exportTime: new Date().toISOString(),
            allContexts: Array.from(this.contextHistory.entries()).map(([key, context]) => ({
                key: key,
                context: context
            })),
            deletedContexts: Array.from(this.deletedContexts.entries()).map(([key, context]) => ({
                key: key,
                context: context
            }))
        };

        this.downloadJSON(exportData, `all_contexts_${this.currentSessionId.substring(8, 16)}.json`);
        this.showNotification('导出完成', `已导出所有上下文数据`, 'success');
    }

    // 清理冗余上下文
    cleanRedundantContexts() {
        if (!confirm('确定要清理冗余的上下文数据吗？此操作将永久删除已标记删除的上下文。')) {
            return;
        }

        const deletedCount = this.deletedContexts.size;
        this.deletedContexts.clear();
        this.saveContextHistory();

        this.showNotification('清理完成', `已永久删除 ${deletedCount} 个冗余上下文`, 'success');
    }

    // 获取选中的上下文
    getSelectedContexts() {
        const selected = [];
        this.contextList.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
            const key = checkbox.id.replace('context_', '');
            selected.push(key);
        });
        return selected;
    }

    // 更新上下文选择状态
    updateContextSelection() {
        const selected = this.getSelectedContexts();
        // 这里可以更新UI状态，比如启用/禁用批量操作按钮
    }

    // 保存上下文更改
    saveContextChanges() {
        this.saveContextHistory();
        this.closeContextManagerModal();
        this.showNotification('保存成功', '上下文更改已保存', 'success');
    }

    // 取消上下文更改
    cancelContextChanges() {
        this.loadContextHistory();
        this.closeContextManagerModal();
        this.showNotification('已取消', '上下文更改已取消', 'info');
    }

    // 下载JSON文件
    downloadJSON(data, filename) {
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.aiChatInstance = new AIChat();
        console.log('✅ AIChat应用初始化成功');
    } catch (error) {
        console.error('❌ AIChat应用初始化失败:', error);
    }
});
