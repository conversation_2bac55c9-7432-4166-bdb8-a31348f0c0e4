<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天助手 - 智能对话与JSON上下文管理</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-robot"></i> AI助手</h2>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <!-- API配置区域 -->
            <div class="config-section">
                <h3><i class="fas fa-cog"></i> API配置</h3>
                <div class="config-form">
                    <div class="form-group">
                        <label for="apiProvider">AI服务商</label>
                        <select id="apiProvider">
                            <option value="openai">OpenAI GPT</option>
                            <option value="claude">Anthropic Claude</option>
                            <option value="deepseek">DeepSeek V3 (Chat)</option>
                            <option value="deepseek-r1">DeepSeek R1 (Reasoning)</option>
                            <option value="gemini">Google Gemini Pro</option>
                            <option value="gemini-2-flash">Google Gemini 2.0 Flash</option>
                            <option value="qwen">通义千问</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="apiKey">API密钥</label>
                        <input type="password" id="apiKey" placeholder="输入API密钥">
                        <button type="button" id="toggleApiKey" class="toggle-password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    
                    <div class="form-group">
                        <label for="apiEndpoint">API端点</label>
                        <input type="url" id="apiEndpoint" placeholder="https://api.openai.com/v1">
                    </div>
                    
                    <div class="form-group">
                        <label for="modelName">模型名称</label>
                        <input type="text" id="modelName" placeholder="gpt-3.5-turbo">
                    </div>
                    
                    <button id="saveConfig" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存配置
                    </button>
                </div>
            </div>
            
            <!-- 自动上下文管理区域 -->
            <div class="context-section">
                <h3><i class="fas fa-brain"></i> 智能上下文</h3>

                <!-- 当前会话信息 -->
                <div class="current-session">
                    <div class="session-info">
                        <span class="session-label">当前会话:</span>
                        <span class="session-name" id="currentSessionName">新对话</span>
                    </div>
                    <div class="session-stats">
                        <span class="context-count" id="contextCount">0轮上下文</span>
                    </div>
                </div>

                <!-- 自动上下文设置 -->
                <div class="auto-context-settings">
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoContextEnabled" checked>
                            <span class="checkmark"></span>
                            自动生成上下文
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="includeUserMessages" checked>
                            <span class="checkmark"></span>
                            包含用户消息
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="includeAIResponses" checked>
                            <span class="checkmark"></span>
                            包含AI回复
                        </label>
                    </div>
                </div>

                <!-- 上下文管理按钮 -->
                <div class="context-controls">
                    <button id="viewContextBtn" class="btn btn-primary">
                        <i class="fas fa-eye"></i> 查看上下文
                    </button>
                    <button id="exportContextBtn" class="btn btn-secondary">
                        <i class="fas fa-download"></i> 导出上下文
                    </button>
                    <button id="cleanContextBtn" class="btn btn-warning">
                        <i class="fas fa-broom"></i> 清理上下文
                    </button>
                </div>
            </div>

            <!-- 传统JSON文件管理区域 -->
            <div class="json-section">
                <h3><i class="fas fa-file-code"></i> 外部JSON文件</h3>
                <div class="json-upload">
                    <input type="file" id="jsonFileInput" accept=".json" multiple style="display: none;">
                    <button id="uploadJsonBtn" class="btn btn-secondary">
                        <i class="fas fa-upload"></i> 上传JSON
                    </button>
                </div>

                <div class="json-files-list" id="jsonFilesList">
                    <!-- JSON文件列表将在这里动态生成 -->
                </div>
            </div>
            
            <!-- 聊天历史管理 -->
            <div class="history-section">
                <h3><i class="fas fa-history"></i> 聊天历史</h3>
                <div class="history-controls">
                    <button id="clearHistory" class="btn btn-danger">
                        <i class="fas fa-trash"></i> 清空历史
                    </button>
                    <button id="exportHistory" class="btn btn-secondary">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>
        </aside>
        
        <!-- 主聊天区域 -->
        <main class="chat-container">
            <!-- 聊天头部 -->
            <header class="chat-header">
                <div class="chat-title">
                    <h1>AI智能对话</h1>
                    <div class="status-indicator" id="statusIndicator">
                        <span class="status-dot"></span>
                        <span class="status-text">就绪</span>
                    </div>
                </div>
                <div class="chat-controls">
                    <button id="newChatBtn" class="btn btn-outline">
                        <i class="fas fa-plus"></i> 新对话
                    </button>
                </div>
            </header>
            
            <!-- 聊天消息区域 -->
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h2>欢迎使用AI聊天助手</h2>
                    <p>您可以：</p>
                    <ul>
                        <li>与AI进行智能对话</li>
                        <li>上传JSON文件作为上下文</li>
                        <li>配置不同的AI服务商</li>
                        <li>管理聊天历史记录</li>
                    </ul>
                    <p>开始对话吧！</p>
                </div>
            </div>
            
            <!-- 输入区域 -->
            <div class="chat-input-container">
                <div class="input-wrapper">
                    <textarea 
                        id="messageInput" 
                        placeholder="输入您的消息... (Shift+Enter换行，Enter发送)"
                        rows="1"
                    ></textarea>
                    <div class="input-actions">
                        <button id="attachJsonBtn" class="btn-icon" title="附加JSON上下文">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <button id="sendMessageBtn" class="btn-icon btn-send" title="发送消息">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
                <div class="input-footer">
                    <div class="attached-context" id="attachedContext" style="display: none;">
                        <span class="context-label">已附加上下文:</span>
                        <span class="context-files" id="contextFiles"></span>
                        <button class="remove-context" id="removeContext">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 上下文管理模态框 -->
    <div class="modal" id="contextManagerModal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3 id="contextManagerTitle">智能上下文管理器</h3>
                <button class="modal-close" id="closeContextManager">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="context-manager-controls">
                    <div class="control-group">
                        <button id="selectAllContextBtn" class="btn btn-secondary">
                            <i class="fas fa-check-square"></i> 全选
                        </button>
                        <button id="deselectAllContextBtn" class="btn btn-secondary">
                            <i class="fas fa-square"></i> 取消全选
                        </button>
                        <button id="deleteSelectedContextBtn" class="btn btn-danger">
                            <i class="fas fa-trash"></i> 删除选中
                        </button>
                        <button id="restoreContextBtn" class="btn btn-success">
                            <i class="fas fa-undo"></i> 恢复删除
                        </button>
                    </div>
                    <div class="control-group">
                        <button id="compactContextBtn" class="btn btn-warning">
                            <i class="fas fa-compress"></i> 压缩冗余
                        </button>
                        <button id="exportSelectedContextBtn" class="btn btn-primary">
                            <i class="fas fa-download"></i> 导出选中
                        </button>
                    </div>
                </div>

                <div class="context-list" id="contextList">
                    <!-- 上下文列表将在这里动态生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="saveContextChangesBtn" class="btn btn-primary">
                    <i class="fas fa-save"></i> 保存更改
                </button>
                <button id="cancelContextChangesBtn" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </button>
            </div>
        </div>
    </div>

    <!-- JSON查看器模态框 -->
    <div class="modal" id="jsonViewerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="jsonViewerTitle">JSON查看器</h3>
                <button class="modal-close" id="closeJsonViewer">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="json-viewer-controls">
                    <button id="editJsonBtn" class="btn btn-primary">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button id="formatJsonBtn" class="btn btn-secondary">
                        <i class="fas fa-code"></i> 格式化
                    </button>
                    <button id="validateJsonBtn" class="btn btn-secondary">
                        <i class="fas fa-check"></i> 验证
                    </button>
                </div>
                <div class="json-content">
                    <pre id="jsonDisplay"></pre>
                    <textarea id="jsonEditor" style="display: none;"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button id="saveJsonBtn" class="btn btn-primary" style="display: none;">
                    <i class="fas fa-save"></i> 保存
                </button>
                <button id="cancelEditBtn" class="btn btn-secondary" style="display: none;">
                    <i class="fas fa-times"></i> 取消
                </button>
            </div>
        </div>
    </div>
    
    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>AI正在思考中...</p>
        </div>
    </div>
    
    <!-- 通知容器 -->
    <div class="notification-container" id="notificationContainer"></div>
    
    <script src="script.js"></script>
</body>
</html>
