<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手动功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f2f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 AI聊天应用手动功能测试</h1>
        
        <div class="test-section">
            <h2>📱 应用预览</h2>
            <p>在下面的iframe中可以直接操作应用：</p>
            <iframe src="http://localhost:8000" id="appFrame"></iframe>
        </div>
        
        <div class="test-section">
            <h2>🔧 功能测试</h2>
            <p>点击下面的按钮来测试各项功能：</p>
            
            <button class="test-button" onclick="testBasicElements()">测试基础元素</button>
            <button class="test-button" onclick="testAPIConfig()">测试API配置</button>
            <button class="test-button" onclick="testContextManagement()">测试上下文管理</button>
            <button class="test-button" onclick="testMessageSending()">测试消息发送</button>
            <button class="test-button" onclick="testJSONUpload()">测试JSON上传</button>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            
            <div id="testResults"></div>
        </div>
        
        <div class="test-section">
            <h2>📋 测试步骤说明</h2>
            <ol>
                <li><strong>基础元素测试</strong>：检查页面上的关键元素是否正确显示</li>
                <li><strong>API配置测试</strong>：
                    <ul>
                        <li>选择"DeepSeek V3 (Chat)"</li>
                        <li>输入API密钥：<code>sk-e146e169c9e8403198ad3a7be6c669f8</code></li>
                        <li>点击"保存配置"</li>
                        <li>检查是否出现成功通知</li>
                    </ul>
                </li>
                <li><strong>上下文管理测试</strong>：
                    <ul>
                        <li>检查"智能上下文"区域显示</li>
                        <li>点击"查看上下文"按钮</li>
                        <li>检查上下文管理器是否打开</li>
                    </ul>
                </li>
                <li><strong>消息发送测试</strong>：
                    <ul>
                        <li>在输入框中输入测试消息</li>
                        <li>点击发送按钮</li>
                        <li>检查消息是否显示</li>
                        <li>等待AI回复</li>
                        <li>检查上下文是否自动生成</li>
                    </ul>
                </li>
                <li><strong>JSON上传测试</strong>：
                    <ul>
                        <li>点击"上传JSON"按钮</li>
                        <li>选择JSON文件</li>
                        <li>检查文件是否正确解析和显示</li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🐛 常见问题排查</h2>
            <ul>
                <li><strong>页面空白</strong>：检查浏览器控制台是否有JavaScript错误</li>
                <li><strong>API调用失败</strong>：检查API密钥是否正确，网络是否正常</li>
                <li><strong>上下文未生成</strong>：确保"自动生成上下文"选项已开启</li>
                <li><strong>按钮无响应</strong>：检查事件绑定是否正确</li>
            </ul>
        </div>
    </div>
    
    <script>
        let testResults = document.getElementById('testResults');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            testResults.appendChild(div);
            testResults.scrollTop = testResults.scrollHeight;
        }
        
        function getAppFrame() {
            return document.getElementById('appFrame').contentWindow;
        }
        
        function testBasicElements() {
            addResult('开始测试基础元素...', 'info');
            
            try {
                const frame = getAppFrame();
                const elements = [
                    'sidebar', 'apiProvider', 'apiKey', 'saveConfig',
                    'currentSessionName', 'contextCount', 'autoContextEnabled',
                    'viewContextBtn', 'chatMessages', 'messageInput', 'sendMessageBtn'
                ];
                
                let passed = 0;
                elements.forEach(id => {
                    const element = frame.document.getElementById(id);
                    if (element) {
                        addResult(`✅ 元素 #${id} 存在`, 'success');
                        passed++;
                    } else {
                        addResult(`❌ 元素 #${id} 不存在`, 'error');
                    }
                });
                
                addResult(`基础元素测试完成：${passed}/${elements.length} 通过`, passed === elements.length ? 'success' : 'warning');
                
            } catch (error) {
                addResult(`基础元素测试失败: ${error.message}`, 'error');
            }
        }
        
        function testAPIConfig() {
            addResult('开始测试API配置...', 'info');
            
            try {
                const frame = getAppFrame();
                const apiProvider = frame.document.getElementById('apiProvider');
                const apiKey = frame.document.getElementById('apiKey');
                const saveConfig = frame.document.getElementById('saveConfig');
                
                if (apiProvider && apiKey && saveConfig) {
                    // 选择DeepSeek
                    apiProvider.value = 'deepseek';
                    apiProvider.dispatchEvent(new Event('change'));
                    addResult('✅ 选择DeepSeek提供商', 'success');
                    
                    // 填入API密钥
                    apiKey.value = 'sk-e146e169c9e8403198ad3a7be6c669f8';
                    addResult('✅ 填入API密钥', 'success');
                    
                    // 保存配置
                    saveConfig.click();
                    addResult('✅ 点击保存配置', 'success');
                    
                    // 检查通知
                    setTimeout(() => {
                        const notification = frame.document.querySelector('.notification');
                        if (notification) {
                            addResult('✅ 配置保存通知出现', 'success');
                        } else {
                            addResult('⚠️ 未检测到保存通知', 'warning');
                        }
                    }, 2000);
                    
                } else {
                    addResult('❌ API配置元素缺失', 'error');
                }
                
            } catch (error) {
                addResult(`API配置测试失败: ${error.message}`, 'error');
            }
        }
        
        function testContextManagement() {
            addResult('开始测试上下文管理...', 'info');
            
            try {
                const frame = getAppFrame();
                const currentSessionName = frame.document.getElementById('currentSessionName');
                const contextCount = frame.document.getElementById('contextCount');
                const viewContextBtn = frame.document.getElementById('viewContextBtn');
                
                if (currentSessionName && contextCount && viewContextBtn) {
                    addResult(`✅ 当前会话: ${currentSessionName.textContent}`, 'success');
                    addResult(`✅ 上下文数量: ${contextCount.textContent}`, 'success');
                    
                    // 点击查看上下文
                    viewContextBtn.click();
                    addResult('✅ 点击查看上下文按钮', 'success');
                    
                    // 检查模态框
                    setTimeout(() => {
                        const modal = frame.document.getElementById('contextManagerModal');
                        if (modal && modal.classList.contains('show')) {
                            addResult('✅ 上下文管理器打开成功', 'success');
                            
                            // 关闭模态框
                            const closeBtn = frame.document.getElementById('closeContextManager');
                            if (closeBtn) {
                                closeBtn.click();
                                addResult('✅ 关闭上下文管理器', 'success');
                            }
                        } else {
                            addResult('❌ 上下文管理器未打开', 'error');
                        }
                    }, 1000);
                    
                } else {
                    addResult('❌ 上下文管理元素缺失', 'error');
                }
                
            } catch (error) {
                addResult(`上下文管理测试失败: ${error.message}`, 'error');
            }
        }
        
        function testMessageSending() {
            addResult('开始测试消息发送...', 'info');
            
            try {
                const frame = getAppFrame();
                const messageInput = frame.document.getElementById('messageInput');
                const sendBtn = frame.document.getElementById('sendMessageBtn');
                
                if (messageInput && sendBtn) {
                    // 输入测试消息
                    messageInput.value = '你好，这是一个功能测试消息，请简单回复确认收到。';
                    messageInput.dispatchEvent(new Event('input'));
                    addResult('✅ 输入测试消息', 'success');
                    
                    // 发送消息
                    sendBtn.click();
                    addResult('✅ 点击发送按钮', 'success');
                    
                    // 检查用户消息是否显示
                    setTimeout(() => {
                        const userMessage = frame.document.querySelector('.message.user');
                        if (userMessage) {
                            addResult('✅ 用户消息已显示', 'success');
                        } else {
                            addResult('❌ 用户消息未显示', 'error');
                        }
                    }, 1000);
                    
                    // 等待AI回复
                    addResult('⏳ 等待AI回复（最多30秒）...', 'info');
                    setTimeout(() => {
                        const aiMessage = frame.document.querySelector('.message.assistant');
                        if (aiMessage) {
                            addResult('✅ AI回复已显示', 'success');
                            
                            // 检查上下文是否更新
                            setTimeout(() => {
                                const contextCount = frame.document.getElementById('contextCount');
                                if (contextCount) {
                                    addResult(`✅ 上下文已更新: ${contextCount.textContent}`, 'success');
                                }
                            }, 2000);
                        } else {
                            addResult('⚠️ AI回复超时或失败', 'warning');
                        }
                    }, 30000);
                    
                } else {
                    addResult('❌ 消息发送元素缺失', 'error');
                }
                
            } catch (error) {
                addResult(`消息发送测试失败: ${error.message}`, 'error');
            }
        }
        
        function testJSONUpload() {
            addResult('开始测试JSON上传...', 'info');
            
            try {
                const frame = getAppFrame();
                const uploadBtn = frame.document.getElementById('uploadJsonBtn');
                const fileInput = frame.document.getElementById('jsonFileInput');
                
                if (uploadBtn && fileInput) {
                    addResult('✅ JSON上传元素存在', 'success');
                    addResult('ℹ️ 请手动测试文件上传功能', 'info');
                    
                    // 点击上传按钮
                    uploadBtn.click();
                    addResult('✅ 点击上传按钮', 'success');
                    
                } else {
                    addResult('❌ JSON上传元素缺失', 'error');
                }
                
            } catch (error) {
                addResult(`JSON上传测试失败: ${error.message}`, 'error');
            }
        }
        
        function runAllTests() {
            addResult('🚀 开始运行所有测试...', 'info');
            testResults.innerHTML = '';
            
            setTimeout(() => testBasicElements(), 500);
            setTimeout(() => testAPIConfig(), 2000);
            setTimeout(() => testContextManagement(), 4000);
            setTimeout(() => testMessageSending(), 6000);
            setTimeout(() => testJSONUpload(), 8000);
            
            setTimeout(() => {
                addResult('🎉 所有测试完成！请查看上面的结果。', 'success');
            }, 10000);
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            addResult('📱 测试页面加载完成，可以开始测试', 'info');
            addResult('💡 建议先运行"测试基础元素"确保页面正常加载', 'info');
        };
    </script>
</body>
</html>
