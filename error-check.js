// 简单的错误检查脚本
console.log('🔍 开始检查JavaScript错误...');

// 检查全局错误
window.addEventListener('error', function(e) {
    console.error('❌ JavaScript错误:', e.error);
    console.error('文件:', e.filename);
    console.error('行号:', e.lineno);
    console.error('列号:', e.colno);
});

// 检查未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(e) {
    console.error('❌ 未处理的Promise拒绝:', e.reason);
});

// 检查DOM加载完成后的状态
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ DOM加载完成');
    
    // 检查关键元素是否存在
    const elements = [
        'sidebar', 'apiProvider', 'apiKey', 'saveConfig',
        'currentSessionName', 'contextCount', 'autoContextEnabled',
        'viewContextBtn', 'chatMessages', 'messageInput', 'sendMessageBtn',
        'attachedContext', 'contextFiles', 'removeContext'
    ];
    
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            console.log(`✅ 元素 #${id} 存在`);
        } else {
            console.error(`❌ 元素 #${id} 不存在`);
        }
    });
    
    // 检查AIChat类是否正确初始化
    setTimeout(() => {
        if (window.aiChatInstance) {
            console.log('✅ AIChat实例已创建');
            console.log('会话ID:', window.aiChatInstance.currentSessionId);
            console.log('消息数量:', window.aiChatInstance.messages.length);
            console.log('上下文数量:', window.aiChatInstance.contextHistory.size);
        } else {
            console.error('❌ AIChat实例未创建');
        }
    }, 1000);
});

console.log('🔍 错误检查脚本加载完成');
