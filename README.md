# AI聊天助手 - 智能对话与JSON上下文管理

一个现代化的Web应用，支持与多种AI大模型进行对话，并提供JSON文件上传、查看、编辑和管理功能，作为AI对话的上下文数据。

## 🌟 主要功能

### 💬 智能聊天
- **多轮对话**：支持连续对话，保持上下文连续性
- **实时通信**：即时消息发送和接收
- **消息历史**：自动保存和管理聊天记录
- **Markdown渲染**：支持格式化文本显示

### 🔌 多API集成
- **OpenAI GPT**：支持GPT-3.5、GPT-4等模型
- **Anthropic Claude**：支持Claude-3系列模型
- **DeepSeek V3**：支持DeepSeek-V3-0324对话模型
- **DeepSeek R1**：支持DeepSeek-R1-0528推理模型
- **Google Gemini Pro**：支持Gemini Pro模型
- **Google Gemini 2.0 Flash**：支持最新Gemini 2.0 Flash模型
- **通义千问**：支持阿里云通义千问模型
- **自定义API**：支持其他兼容OpenAI格式的API

### 📄 JSON上下文管理
- **文件上传**：支持拖拽或点击上传JSON文件
- **可视化查看**：语法高亮的JSON查看器
- **实时编辑**：内置编辑器支持JSON编辑和验证
- **上下文附加**：将JSON数据作为对话上下文传递给AI
- **多文件管理**：支持同时管理多个JSON文件

### 🎨 现代化界面
- **响应式设计**：完美适配桌面和移动设备
- **现代UI**：渐变色、圆角、阴影等现代设计元素
- **暗色主题**：JSON查看器采用暗色主题
- **动画效果**：流畅的过渡动画和交互反馈

## 🚀 快速开始

### 1. 部署应用
```bash
# 克隆或下载项目文件
# 将以下文件放在同一目录下：
# - index.html
# - styles.css
# - script.js
# - README.md

# 使用任何Web服务器运行，例如：
# 使用Python内置服务器
python -m http.server 8000

# 使用Node.js serve
npx serve .

# 或直接用浏览器打开index.html文件
```

### 2. 配置API
1. 点击左侧边栏的"API配置"区域
2. 选择您要使用的AI服务商
3. 输入对应的API密钥
4. 确认API端点和模型名称
5. 点击"保存配置"

### 3. 开始对话
1. 在底部输入框中输入您的消息
2. 按Enter键或点击发送按钮
3. 等待AI回复

### 4. 使用JSON上下文
1. 点击"上传JSON"按钮选择文件
2. 在文件列表中点击"附加到对话"
3. JSON内容将作为上下文传递给AI

## 📋 API配置说明

### OpenAI GPT
```
API端点: https://api.openai.com/v1
模型名称: gpt-3.5-turbo 或 gpt-4
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### Anthropic Claude
```
API端点: https://api.anthropic.com/v1
模型名称: claude-3-sonnet-20240229
API密钥: sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### DeepSeek V3
```
API端点: https://api.deepseek.com
模型名称: deepseek-chat
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### DeepSeek R1
```
API端点: https://api.deepseek.com
模型名称: deepseek-reasoner
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### Google Gemini Pro
```
API端点: https://generativelanguage.googleapis.com/v1beta
模型名称: gemini-pro
API密钥: AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### Google Gemini 2.0 Flash
```
API端点: https://generativelanguage.googleapis.com/v1beta
模型名称: gemini-2.0-flash
API密钥: AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 通义千问
```
API端点: https://dashscope.aliyuncs.com/api/v1
模型名称: qwen-turbo
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

## 🎯 使用技巧

### 键盘快捷键
- **Enter**：发送消息
- **Shift + Enter**：换行
- **Ctrl/Cmd + K**：聚焦到输入框
- **Escape**：关闭模态框

### JSON上下文最佳实践
1. **结构化数据**：确保JSON格式正确且结构清晰
2. **相关性**：上传与对话主题相关的JSON数据
3. **大小控制**：避免上传过大的JSON文件影响性能
4. **多文件组合**：可以同时附加多个相关的JSON文件

### 对话优化
1. **清晰表达**：使用清晰、具体的问题
2. **上下文利用**：充分利用JSON上下文数据
3. **分步骤**：复杂问题可以分步骤询问
4. **反馈调整**：根据AI回复调整后续问题

## 🔧 技术架构

### 前端技术栈
- **HTML5**：语义化标记和现代Web标准
- **CSS3**：现代CSS特性（Grid、Flexbox、CSS变量）
- **JavaScript ES6+**：原生JavaScript，无框架依赖
- **Font Awesome**：图标库
- **Google Fonts**：Inter字体

### 核心特性
- **模块化设计**：清晰的代码结构和组件分离
- **响应式布局**：适配各种屏幕尺寸
- **本地存储**：使用localStorage保存配置和历史
- **错误处理**：完善的错误处理和用户反馈
- **性能优化**：懒加载和动画性能优化

## 📱 浏览器兼容性

- **Chrome** 80+
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+

## 🔒 隐私和安全

- **本地存储**：所有数据存储在浏览器本地
- **API密钥**：密钥仅在本地存储，不会上传到服务器
- **HTTPS建议**：建议在HTTPS环境下使用
- **数据清理**：支持清空历史记录和配置

## 🐛 故障排除

### 常见问题

**Q: API调用失败怎么办？**
A: 检查API密钥是否正确，网络连接是否正常，API端点是否可访问。

**Q: JSON文件上传失败？**
A: 确保文件格式为.json且内容为有效的JSON格式。

**Q: 消息发送没有反应？**
A: 检查API配置是否完整，特别是API密钥是否已设置。

**Q: 界面显示异常？**
A: 尝试刷新页面，或清除浏览器缓存。

### 调试模式
打开浏览器开发者工具（F12）查看控制台日志获取详细错误信息。

## 📄 许可证

MIT License - 详见LICENSE文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如果您遇到问题或有建议，请：
1. 查看本README文档
2. 检查浏览器控制台错误信息
3. 提交Issue描述问题

---

**享受与AI的智能对话体验！** 🚀
