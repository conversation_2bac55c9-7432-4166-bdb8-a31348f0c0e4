# 🧠 智能上下文管理系统使用指南

## 🌟 功能概述

智能上下文管理系统是AI聊天助手的核心功能，它能够：

- **自动生成**：每轮对话后自动生成JSON上下文文件
- **智能保存**：按会话分组保存，每个会话独立管理
- **批量操作**：支持选择、删除、恢复、导出等批量操作
- **实时编辑**：可以编辑、压缩和优化上下文内容
- **持久存储**：所有数据保存在浏览器本地存储中

## 🚀 快速开始

### 1. 启用自动上下文
在左侧边栏的"智能上下文"区域：
- ✅ **自动生成上下文** - 开启后每轮对话自动生成JSON
- ✅ **包含用户消息** - 在上下文中包含用户的消息
- ✅ **包含AI回复** - 在上下文中包含AI的回复

### 2. 开始对话
- 配置好API后，直接开始对话
- 每发送一条消息并收到AI回复后，系统会自动生成一个JSON上下文文件
- 在"智能上下文"区域可以看到当前会话信息和上下文数量

### 3. 管理上下文
点击"查看上下文"按钮打开上下文管理器，可以：
- 查看所有上下文的详细信息
- 编辑、删除或恢复特定的上下文
- 批量操作多个上下文
- 导出上下文数据

## 📋 上下文JSON结构

每个自动生成的上下文JSON包含以下信息：

```json
{
  "sessionId": "session_1234567890_abcdef123",
  "roundNumber": 1,
  "timestamp": "2025-07-30T18:30:00.000Z",
  "conversation": {
    "user": {
      "message": "用户的消息内容",
      "timestamp": "2025-07-30T18:30:00.000Z",
      "role": "user"
    },
    "assistant": {
      "message": "AI的回复内容",
      "timestamp": "2025-07-30T18:30:01.000Z",
      "role": "assistant"
    }
  },
  "metadata": {
    "messageCount": 2,
    "sessionDuration": 1500,
    "apiProvider": "deepseek",
    "modelName": "deepseek-chat"
  },
  "externalContext": {
    "example-data.json": {
      // 外部JSON文件内容
    }
  }
}
```

## 🛠️ 上下文管理器功能

### 查看和编辑
- **查看详情**：点击👁️图标查看完整的JSON内容
- **编辑内容**：点击✏️图标编辑上下文内容
- **实时预览**：在编辑时可以实时预览格式化后的内容

### 批量操作
- **全选/取消全选**：快速选择所有上下文
- **批量删除**：删除选中的多个上下文（可恢复）
- **批量导出**：导出选中的上下文到JSON文件
- **恢复删除**：恢复所有已删除的上下文

### 智能管理
- **压缩冗余**：自动识别和压缩重复或冗余的内容
- **清理上下文**：永久删除已标记删除的上下文
- **统计信息**：显示总轮数、总大小、已删除数量等统计

## 📁 会话管理

### 自动会话创建
- 每次启动应用或点击"新对话"都会创建新会话
- 每个会话有唯一的ID，格式：`session_时间戳_随机字符`
- 会话信息显示在"智能上下文"区域顶部

### 会话隔离
- 不同会话的上下文完全独立
- 每个会话的数据单独存储
- 切换会话不会影响其他会话的数据

### 会话持久化
- 所有会话数据保存在浏览器本地存储
- 关闭浏览器后重新打开，数据依然存在
- 支持导出会话数据进行备份

## 🎯 使用场景

### 1. 代码开发咨询
```
场景：询问代码问题
上下文：自动保存每轮的问题和解答
用途：后续可以回顾完整的解决过程
```

### 2. 学习和研究
```
场景：深入学习某个主题
上下文：保存每个问题的详细回答
用途：构建完整的知识体系
```

### 3. 项目规划
```
场景：制定项目计划
上下文：记录每个决策点和讨论
用途：追踪项目演进过程
```

### 4. 问题诊断
```
场景：排查复杂问题
上下文：保存诊断步骤和结果
用途：形成问题解决方案库
```

## ⚙️ 高级设置

### 上下文限制
- **最大轮数**：默认保存50轮对话，超出后自动删除最早的
- **大小控制**：可以通过压缩功能减少存储空间
- **选择性保存**：可以关闭用户消息或AI回复的保存

### 性能优化
- **懒加载**：上下文管理器按需加载内容
- **分页显示**：大量上下文时自动分页
- **索引优化**：快速搜索和定位特定上下文

### 数据安全
- **本地存储**：所有数据仅保存在本地浏览器
- **加密选项**：敏感内容可以选择加密存储
- **备份恢复**：支持导出和导入功能

## 🔧 故障排除

### 常见问题

**Q: 上下文没有自动生成？**
A: 检查"自动生成上下文"选项是否开启，确保API调用成功。

**Q: 上下文管理器显示空白？**
A: 确保已经进行过对话，刷新页面重试。

**Q: 无法编辑上下文内容？**
A: 检查JSON格式是否正确，确保没有语法错误。

**Q: 导出的文件无法打开？**
A: 确保文件扩展名为.json，使用文本编辑器或JSON查看器打开。

### 调试技巧
1. 打开浏览器开发者工具（F12）
2. 查看Console标签页的错误信息
3. 检查Application > Local Storage中的数据
4. 使用Network标签页检查API调用状态

## 📊 数据格式说明

### 导出格式
```json
{
  "sessionId": "会话ID",
  "exportTime": "导出时间",
  "allContexts": [
    {
      "key": "round_1",
      "context": { /* 上下文内容 */ }
    }
  ],
  "deletedContexts": [
    {
      "key": "round_2", 
      "context": { /* 已删除的上下文 */ }
    }
  ]
}
```

### 导入要求
- 文件必须是有效的JSON格式
- 包含必要的字段：sessionId, contexts
- 上下文结构必须符合系统要求

## 🎉 最佳实践

### 1. 定期整理
- 定期查看和整理上下文内容
- 删除不必要的冗余信息
- 导出重要的对话记录

### 2. 合理配置
- 根据使用场景调整包含的内容类型
- 设置合适的最大保存轮数
- 定期清理已删除的上下文

### 3. 备份重要数据
- 定期导出重要的会话数据
- 保存到云存储或其他设备
- 建立数据恢复流程

---

**开始体验智能上下文管理，让AI对话更加智能和高效！** 🚀
