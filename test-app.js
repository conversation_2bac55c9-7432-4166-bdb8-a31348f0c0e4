const { chromium } = require('playwright');

async function testAIChatApp() {
    console.log('🚀 开始测试AI聊天应用...');

    let browser;
    try {
        browser = await chromium.launch({
            headless: false,
            slowMo: 500 // 减慢操作速度以便观察
        });

        const context = await browser.newContext();
        const page = await context.newPage();

        // 监听控制台消息
        page.on('console', msg => {
            console.log(`浏览器控制台 [${msg.type()}]: ${msg.text()}`);
        });

        // 监听页面错误
        page.on('pageerror', error => {
            console.error('页面错误:', error.message);
        });
    
    try {
        // 1. 打开应用
        console.log('📱 正在打开应用...');
        await page.goto('http://localhost:8000');
        await page.waitForLoadState('networkidle');
        
        // 检查页面标题
        const title = await page.title();
        console.log(`✅ 页面标题: ${title}`);
        
        // 2. 检查基本元素是否存在
        console.log('🔍 检查基本UI元素...');
        
        const sidebar = await page.locator('#sidebar').isVisible();
        console.log(`侧边栏: ${sidebar ? '✅' : '❌'}`);
        
        const chatMessages = await page.locator('#chatMessages').isVisible();
        console.log(`聊天区域: ${chatMessages ? '✅' : '❌'}`);
        
        const messageInput = await page.locator('#messageInput').isVisible();
        console.log(`输入框: ${messageInput ? '✅' : '❌'}`);
        
        // 3. 检查API配置区域
        console.log('⚙️ 测试API配置功能...');
        
        const apiProvider = page.locator('#apiProvider');
        await apiProvider.selectOption('deepseek');
        console.log('✅ 选择DeepSeek API提供商');
        
        const apiKey = page.locator('#apiKey');
        await apiKey.fill('sk-e146e169c9e8403198ad3a7be6c669f8');
        console.log('✅ 填入API密钥');
        
        const saveConfigBtn = page.locator('#saveConfig');
        await saveConfigBtn.click();
        console.log('✅ 保存配置');
        
        // 等待通知出现
        await page.waitForSelector('.notification', { timeout: 5000 });
        console.log('✅ 配置保存通知出现');
        
        // 4. 检查智能上下文区域
        console.log('🧠 测试智能上下文功能...');
        
        const currentSessionName = await page.locator('#currentSessionName').textContent();
        console.log(`当前会话: ${currentSessionName}`);
        
        const contextCount = await page.locator('#contextCount').textContent();
        console.log(`上下文数量: ${contextCount}`);
        
        // 检查自动上下文设置
        const autoContextEnabled = await page.locator('#autoContextEnabled').isChecked();
        console.log(`自动上下文: ${autoContextEnabled ? '✅ 已启用' : '❌ 未启用'}`);
        
        // 5. 测试发送消息功能
        console.log('💬 测试发送消息功能...');
        
        const messageInput2 = page.locator('#messageInput');
        await messageInput2.fill('你好，请简单介绍一下自己');
        
        const sendBtn = page.locator('#sendMessageBtn');
        await sendBtn.click();
        console.log('✅ 发送测试消息');
        
        // 等待AI回复
        console.log('⏳ 等待AI回复...');
        await page.waitForSelector('.message.assistant', { timeout: 30000 });
        console.log('✅ 收到AI回复');
        
        // 检查上下文是否自动生成
        await page.waitForTimeout(2000); // 等待上下文生成
        const newContextCount = await page.locator('#contextCount').textContent();
        console.log(`更新后的上下文数量: ${newContextCount}`);
        
        // 6. 测试上下文管理器
        console.log('📋 测试上下文管理器...');
        
        const viewContextBtn = page.locator('#viewContextBtn');
        await viewContextBtn.click();
        console.log('✅ 打开上下文管理器');
        
        // 等待模态框出现
        await page.waitForSelector('#contextManagerModal.show', { timeout: 5000 });
        console.log('✅ 上下文管理器模态框出现');
        
        // 检查上下文列表
        const contextItems = await page.locator('.context-item').count();
        console.log(`上下文项目数量: ${contextItems}`);
        
        if (contextItems > 0) {
            // 测试查看上下文详情
            const viewContextDetailBtn = page.locator('.view-context').first();
            await viewContextDetailBtn.click();
            console.log('✅ 查看上下文详情');
            
            await page.waitForSelector('#jsonViewerModal.show', { timeout: 5000 });
            console.log('✅ JSON查看器出现');
            
            // 关闭JSON查看器
            await page.locator('#closeJsonViewer').click();
            await page.waitForTimeout(1000);
        }
        
        // 关闭上下文管理器
        await page.locator('#closeContextManager').click();
        await page.waitForTimeout(1000);
        console.log('✅ 关闭上下文管理器');
        
        // 7. 测试JSON文件上传功能
        console.log('📁 测试JSON文件上传功能...');
        
        // 创建一个测试JSON文件内容
        const testJsonContent = JSON.stringify({
            test: "这是一个测试JSON文件",
            timestamp: new Date().toISOString(),
            data: {
                name: "测试数据",
                value: 123
            }
        }, null, 2);
        
        // 模拟文件上传（注意：这里需要实际的文件路径）
        console.log('⚠️ JSON文件上传功能需要实际文件，跳过此测试');
        
        // 8. 测试新对话功能
        console.log('🆕 测试新对话功能...');
        
        const newChatBtn = page.locator('#newChatBtn');
        await newChatBtn.click();
        console.log('✅ 点击新对话按钮');
        
        // 确认对话框
        page.on('dialog', async dialog => {
            console.log(`对话框: ${dialog.message()}`);
            await dialog.accept();
        });
        
        await page.waitForTimeout(2000);
        
        const newSessionName = await page.locator('#currentSessionName').textContent();
        console.log(`新会话名称: ${newSessionName}`);
        
        // 9. 测试响应式设计
        console.log('📱 测试响应式设计...');
        
        // 切换到移动端视口
        await page.setViewportSize({ width: 375, height: 667 });
        await page.waitForTimeout(1000);
        
        const sidebarToggle = page.locator('#sidebarToggle');
        const isToggleVisible = await sidebarToggle.isVisible();
        console.log(`移动端侧边栏切换按钮: ${isToggleVisible ? '✅ 可见' : '❌ 不可见'}`);
        
        if (isToggleVisible) {
            await sidebarToggle.click();
            console.log('✅ 测试移动端侧边栏切换');
            await page.waitForTimeout(1000);
        }
        
        // 恢复桌面端视口
        await page.setViewportSize({ width: 1280, height: 720 });
        await page.waitForTimeout(1000);
        
        console.log('✅ 所有功能测试完成！');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);

        try {
            // 截图保存错误状态
            await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
            console.log('📸 错误截图已保存为 error-screenshot.png');
        } catch (screenshotError) {
            console.error('截图失败:', screenshotError.message);
        }
    } finally {
        if (browser) {
            // 保持浏览器打开一段时间以便观察
            console.log('⏳ 保持浏览器打开15秒以便观察...');
            await page.waitForTimeout(15000);

            await browser.close();
            console.log('🏁 测试完成，浏览器已关闭');
        }
    }
}

// 运行测试
testAIChatApp().catch(console.error);
