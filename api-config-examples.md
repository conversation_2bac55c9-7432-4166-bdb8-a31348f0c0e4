# API配置示例

本文档提供了各种AI服务商的API配置示例，帮助您快速配置和使用AI聊天助手。

## 🔑 获取API密钥

### OpenAI
1. 访问 [OpenAI Platform](https://platform.openai.com/)
2. 注册并登录账户
3. 进入 API Keys 页面
4. 点击 "Create new secret key"
5. 复制生成的密钥（格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx）

### Anthropic Claude
1. 访问 [Anthropic Console](https://console.anthropic.com/)
2. 注册并登录账户
3. 进入 API Keys 页面
4. 点击 "Create Key"
5. 复制生成的密钥（格式：sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx）

### DeepSeek
1. 访问 [DeepSeek Platform](https://platform.deepseek.com/)
2. 注册并登录账户
3. 进入 API Keys 页面
4. 创建新的API密钥
5. 复制生成的密钥

### Google Gemini
1. 访问 [Google AI Studio](https://makersuite.google.com/)
2. 登录Google账户
3. 点击 "Get API key"
4. 创建新项目或选择现有项目
5. 复制生成的密钥（格式：AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx）

### 通义千问
1. 访问 [阿里云控制台](https://dashscope.console.aliyun.com/)
2. 登录阿里云账户
3. 开通DashScope服务
4. 进入API-KEY管理页面
5. 创建并复制API密钥

## ⚙️ 配置示例

### OpenAI GPT-3.5 Turbo
```
AI服务商: OpenAI GPT
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点: https://api.openai.com/v1
模型名称: gpt-3.5-turbo
```

### OpenAI GPT-4
```
AI服务商: OpenAI GPT
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点: https://api.openai.com/v1
模型名称: gpt-4
```

### Claude 3 Sonnet
```
AI服务商: Anthropic Claude
API密钥: sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点: https://api.anthropic.com/v1
模型名称: claude-3-sonnet-20240229
```

### Claude 3 Opus
```
AI服务商: Anthropic Claude
API密钥: sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点: https://api.anthropic.com/v1
模型名称: claude-3-opus-20240229
```

### DeepSeek V3 Chat
```
AI服务商: DeepSeek V3 (Chat)
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点: https://api.deepseek.com
模型名称: deepseek-chat
```

### DeepSeek R1 Reasoning
```
AI服务商: DeepSeek R1 (Reasoning)
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点: https://api.deepseek.com
模型名称: deepseek-reasoner
```

### Google Gemini Pro
```
AI服务商: Google Gemini Pro
API密钥: AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点: https://generativelanguage.googleapis.com/v1beta
模型名称: gemini-pro
```

### Google Gemini 2.0 Flash
```
AI服务商: Google Gemini 2.0 Flash
API密钥: AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点: https://generativelanguage.googleapis.com/v1beta
模型名称: gemini-2.0-flash
```

### 通义千问 Turbo
```
AI服务商: 通义千问
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点: https://dashscope.aliyuncs.com/api/v1
模型名称: qwen-turbo
```

### 通义千问 Plus
```
AI服务商: 通义千问
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API端点: https://dashscope.aliyuncs.com/api/v1
模型名称: qwen-plus
```

## 🔧 自定义API配置

如果您使用的是其他兼容OpenAI格式的API服务，可以选择"自定义"选项：

```
AI服务商: 自定义
API密钥: 您的API密钥
API端点: 您的API端点URL
模型名称: 您的模型名称
```

### 常见的自定义API服务
- **Azure OpenAI**: `https://your-resource.openai.azure.com/`
- **本地部署的模型**: `http://localhost:8000/v1`
- **其他第三方服务**: 根据服务商提供的文档配置

## 💡 配置建议

### 模型选择建议
- **日常对话**: GPT-3.5-turbo, Claude-3-sonnet, qwen-turbo
- **复杂推理**: GPT-4, Claude-3-opus, qwen-plus
- **代码相关**: DeepSeek-coder, GPT-4
- **多语言支持**: Gemini-pro, qwen系列

### 成本考虑
- **经济型**: GPT-3.5-turbo, qwen-turbo
- **平衡型**: Claude-3-sonnet, deepseek-chat
- **高性能**: GPT-4, Claude-3-opus

### 响应速度
- **最快**: qwen-turbo, deepseek-chat
- **中等**: GPT-3.5-turbo, gemini-pro
- **较慢**: GPT-4, Claude-3-opus

## 🚨 注意事项

### 安全提醒
1. **保护API密钥**: 不要在公共场所或代码中暴露API密钥
2. **定期更换**: 建议定期更换API密钥
3. **权限控制**: 为API密钥设置适当的权限和使用限制
4. **监控使用**: 定期检查API使用情况和费用

### 使用限制
1. **频率限制**: 注意各服务商的API调用频率限制
2. **配额管理**: 监控API使用配额，避免超额
3. **内容政策**: 遵守各服务商的内容使用政策
4. **地区限制**: 某些服务可能有地区访问限制

### 故障排除
1. **网络问题**: 确保网络连接正常，可以访问API端点
2. **密钥错误**: 检查API密钥格式和有效性
3. **模型名称**: 确认模型名称拼写正确
4. **端点URL**: 验证API端点URL是否正确

## 📞 技术支持

如果您在配置过程中遇到问题：

1. **检查控制台**: 打开浏览器开发者工具查看错误信息
2. **测试连接**: 使用curl或Postman测试API连接
3. **查看文档**: 参考各服务商的官方API文档
4. **社区支持**: 在相关技术社区寻求帮助

---

**祝您使用愉快！** 🎉
