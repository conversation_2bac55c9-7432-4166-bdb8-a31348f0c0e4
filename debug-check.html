<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试检查页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 AI聊天应用调试检查</h1>
    
    <div class="debug-section">
        <h2>基础检查</h2>
        <div id="basicChecks"></div>
    </div>
    
    <div class="debug-section">
        <h2>功能测试</h2>
        <button onclick="testAPIConfig()">测试API配置</button>
        <button onclick="testContextManagement()">测试上下文管理</button>
        <button onclick="testMessageInput()">测试消息输入</button>
        <button onclick="testJSONUpload()">测试JSON上传</button>
        <button onclick="runAllTests()">运行所有测试</button>
    </div>
    
    <div class="debug-section">
        <h2>测试输出</h2>
        <div id="output"></div>
    </div>
    
    <script>
        let output = document.getElementById('output');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            output.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function checkBasics() {
            const basicChecks = document.getElementById('basicChecks');
            let html = '';
            
            // 检查主应用是否可访问
            fetch('http://localhost:8000')
                .then(response => {
                    if (response.ok) {
                        html += '<div class="status success">✅ 主应用服务器正常运行</div>';
                    } else {
                        html += '<div class="status error">❌ 主应用服务器响应异常</div>';
                    }
                    basicChecks.innerHTML = html;
                })
                .catch(error => {
                    html += '<div class="status error">❌ 无法连接到主应用服务器</div>';
                    basicChecks.innerHTML = html;
                });
            
            // 检查关键元素
            const elements = [
                'sidebar', 'apiProvider', 'apiKey', 'saveConfig',
                'currentSessionName', 'contextCount', 'autoContextEnabled',
                'viewContextBtn', 'chatMessages', 'messageInput', 'sendMessageBtn'
            ];
            
            setTimeout(() => {
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        html += `<div class="status success">✅ 元素 #${id} 存在</div>`;
                    } else {
                        html += `<div class="status error">❌ 元素 #${id} 不存在</div>`;
                    }
                });
                basicChecks.innerHTML = html;
            }, 1000);
        }
        
        function testAPIConfig() {
            log('开始测试API配置功能...');
            
            try {
                // 在新窗口中打开主应用
                const mainApp = window.open('http://localhost:8000', '_blank');
                
                setTimeout(() => {
                    try {
                        const apiProvider = mainApp.document.getElementById('apiProvider');
                        const apiKey = mainApp.document.getElementById('apiKey');
                        const saveConfig = mainApp.document.getElementById('saveConfig');
                        
                        if (apiProvider && apiKey && saveConfig) {
                            log('API配置元素检查通过', 'success');
                            
                            // 测试选择DeepSeek
                            apiProvider.value = 'deepseek';
                            apiProvider.dispatchEvent(new Event('change'));
                            log('选择DeepSeek提供商', 'success');
                            
                            // 测试填入API密钥
                            apiKey.value = 'sk-test-key';
                            log('填入测试API密钥', 'success');
                            
                            // 测试保存配置
                            saveConfig.click();
                            log('点击保存配置按钮', 'success');
                            
                        } else {
                            log('API配置元素缺失', 'error');
                        }
                    } catch (error) {
                        log(`API配置测试失败: ${error.message}`, 'error');
                    }
                }, 2000);
                
            } catch (error) {
                log(`无法打开主应用: ${error.message}`, 'error');
            }
        }
        
        function testContextManagement() {
            log('开始测试上下文管理功能...');
            
            try {
                const mainApp = window.open('http://localhost:8000', '_blank');
                
                setTimeout(() => {
                    try {
                        const currentSessionName = mainApp.document.getElementById('currentSessionName');
                        const contextCount = mainApp.document.getElementById('contextCount');
                        const viewContextBtn = mainApp.document.getElementById('viewContextBtn');
                        
                        if (currentSessionName && contextCount && viewContextBtn) {
                            log('上下文管理元素检查通过', 'success');
                            log(`当前会话: ${currentSessionName.textContent}`, 'info');
                            log(`上下文数量: ${contextCount.textContent}`, 'info');
                            
                            // 测试打开上下文管理器
                            viewContextBtn.click();
                            log('点击查看上下文按钮', 'success');
                            
                            setTimeout(() => {
                                const modal = mainApp.document.getElementById('contextManagerModal');
                                if (modal && modal.classList.contains('show')) {
                                    log('上下文管理器模态框打开成功', 'success');
                                } else {
                                    log('上下文管理器模态框未打开', 'warning');
                                }
                            }, 1000);
                            
                        } else {
                            log('上下文管理元素缺失', 'error');
                        }
                    } catch (error) {
                        log(`上下文管理测试失败: ${error.message}`, 'error');
                    }
                }, 2000);
                
            } catch (error) {
                log(`无法打开主应用: ${error.message}`, 'error');
            }
        }
        
        function testMessageInput() {
            log('开始测试消息输入功能...');
            
            try {
                const mainApp = window.open('http://localhost:8000', '_blank');
                
                setTimeout(() => {
                    try {
                        const messageInput = mainApp.document.getElementById('messageInput');
                        const sendBtn = mainApp.document.getElementById('sendMessageBtn');
                        
                        if (messageInput && sendBtn) {
                            log('消息输入元素检查通过', 'success');
                            
                            // 测试输入消息
                            messageInput.value = '这是一个测试消息';
                            messageInput.dispatchEvent(new Event('input'));
                            log('输入测试消息', 'success');
                            
                            // 测试发送按钮
                            sendBtn.click();
                            log('点击发送按钮', 'success');
                            
                        } else {
                            log('消息输入元素缺失', 'error');
                        }
                    } catch (error) {
                        log(`消息输入测试失败: ${error.message}`, 'error');
                    }
                }, 2000);
                
            } catch (error) {
                log(`无法打开主应用: ${error.message}`, 'error');
            }
        }
        
        function testJSONUpload() {
            log('开始测试JSON上传功能...');
            
            try {
                const mainApp = window.open('http://localhost:8000', '_blank');
                
                setTimeout(() => {
                    try {
                        const uploadBtn = mainApp.document.getElementById('uploadJsonBtn');
                        const fileInput = mainApp.document.getElementById('jsonFileInput');
                        
                        if (uploadBtn && fileInput) {
                            log('JSON上传元素检查通过', 'success');
                            
                            // 测试点击上传按钮
                            uploadBtn.click();
                            log('点击JSON上传按钮', 'success');
                            
                        } else {
                            log('JSON上传元素缺失', 'error');
                        }
                    } catch (error) {
                        log(`JSON上传测试失败: ${error.message}`, 'error');
                    }
                }, 2000);
                
            } catch (error) {
                log(`无法打开主应用: ${error.message}`, 'error');
            }
        }
        
        function runAllTests() {
            log('开始运行所有测试...', 'info');
            output.textContent = '';
            
            setTimeout(() => testAPIConfig(), 1000);
            setTimeout(() => testContextManagement(), 3000);
            setTimeout(() => testMessageInput(), 5000);
            setTimeout(() => testJSONUpload(), 7000);
            
            setTimeout(() => {
                log('所有测试完成！', 'success');
            }, 9000);
        }
        
        // 页面加载时执行基础检查
        window.onload = function() {
            log('调试页面加载完成', 'success');
            checkBasics();
        };
    </script>
</body>
</html>
