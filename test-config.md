# 测试配置说明

## 🔑 提供的API密钥

### DeepSeek API
```
API密钥: ***********************************
API端点: https://api.deepseek.com
```

**可用模型：**
- `deepseek-chat` (DeepSeek V3) - 对话模型
- `deepseek-reasoner` (DeepSeek R1) - 推理模型

### Google Gemini API
```
API密钥1: AIzaSyA_7e5i1X0Qwqt64GM0YxAu4AuUBXlM2gU
API密钥2: AIzaSyDL-n-QZrMGnBdZPuuX7XMJ5htg-jEA8Bc
API端点: https://generativelanguage.googleapis.com/v1beta
```

**可用模型：**
- `gemini-pro` - Gemini Pro模型
- `gemini-2.0-flash` - Gemini 2.0 Flash模型

## 🧪 测试步骤

### 1. 测试DeepSeek V3 Chat
1. 在应用中选择 "DeepSeek V3 (Chat)"
2. 输入API密钥: `***********************************`
3. 确认API端点: `https://api.deepseek.com`
4. 确认模型名称: `deepseek-chat`
5. 保存配置并测试对话

### 2. 测试DeepSeek R1 Reasoning
1. 在应用中选择 "DeepSeek R1 (Reasoning)"
2. 输入API密钥: `***********************************`
3. 确认API端点: `https://api.deepseek.com`
4. 确认模型名称: `deepseek-reasoner`
5. 保存配置并测试推理能力

### 3. 测试Google Gemini Pro
1. 在应用中选择 "Google Gemini Pro"
2. 输入API密钥: `AIzaSyA_7e5i1X0Qwqt64GM0YxAu4AuUBXlM2gU`
3. 确认API端点: `https://generativelanguage.googleapis.com/v1beta`
4. 确认模型名称: `gemini-pro`
5. 保存配置并测试对话

### 4. 测试Google Gemini 2.0 Flash
1. 在应用中选择 "Google Gemini 2.0 Flash"
2. 输入API密钥: `AIzaSyDL-n-QZrMGnBdZPuuX7XMJ5htg-jEA8Bc`
3. 确认API端点: `https://generativelanguage.googleapis.com/v1beta`
4. 确认模型名称: `gemini-2.0-flash`
5. 保存配置并测试对话

## 🔍 API调用格式验证

### DeepSeek API调用示例
```bash
curl https://api.deepseek.com/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ***********************************" \
  -d '{
    "model": "deepseek-chat",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ]
  }'
```

### Gemini API调用示例
```bash
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent" \
  -H 'Content-Type: application/json' \
  -H 'X-goog-api-key: AIzaSyDL-n-QZrMGnBdZPuuX7XMJ5htg-jEA8Bc' \
  -X POST \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Explain how AI works in a few words"
          }
        ]
      }
    ]
  }'
```

## 📝 测试建议

### 对话测试
1. **简单问候**: "你好，请介绍一下自己"
2. **推理测试**: "请解释一下量子计算的基本原理"
3. **代码生成**: "用Python写一个快速排序算法"
4. **JSON上下文测试**: 上传example-data.json文件，询问项目相关问题

### 功能测试
1. **JSON文件上传**: 测试上传和查看JSON文件
2. **上下文附加**: 测试将JSON作为对话上下文
3. **历史管理**: 测试清空和导出聊天历史
4. **响应式设计**: 在不同屏幕尺寸下测试界面

## ⚠️ 注意事项

### API限制
- **DeepSeek**: 注意token使用量和频率限制
- **Gemini**: 某些地区可能有访问限制
- **网络**: 确保网络连接稳定

### 错误处理
- 如果API调用失败，检查控制台错误信息
- 验证API密钥格式和有效性
- 确认模型名称拼写正确

### 性能优化
- **DeepSeek R1**: 推理模型响应时间较长，请耐心等待
- **Gemini 2.0 Flash**: 新模型，响应速度较快
- **上下文长度**: 注意不要超过模型的上下文限制

## 🚀 快速测试命令

在浏览器控制台中运行以下代码快速测试API连接：

```javascript
// 测试DeepSeek API
fetch('https://api.deepseek.com/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ***********************************'
  },
  body: JSON.stringify({
    model: 'deepseek-chat',
    messages: [{ role: 'user', content: 'Hello' }],
    max_tokens: 100
  })
}).then(r => r.json()).then(console.log);

// 测试Gemini API
fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-goog-api-key': 'AIzaSyDL-n-QZrMGnBdZPuuX7XMJ5htg-jEA8Bc'
  },
  body: JSON.stringify({
    contents: [{ parts: [{ text: 'Hello' }] }]
  })
}).then(r => r.json()).then(console.log);
```

---

**准备好测试最新的AI模型了！** 🎉
