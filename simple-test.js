const { chromium } = require('playwright');

async function simpleTest() {
    console.log('🚀 开始简单测试...');
    
    let browser;
    try {
        browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000
        });
        
        console.log('✅ 浏览器启动成功');
        
        const context = await browser.newContext();
        const page = await context.newPage();
        
        console.log('✅ 页面创建成功');
        
        // 打开应用
        console.log('📱 正在打开 http://localhost:8000...');
        await page.goto('http://localhost:8000', { waitUntil: 'networkidle' });
        
        console.log('✅ 页面加载完成');
        
        // 检查页面标题
        const title = await page.title();
        console.log(`📄 页面标题: ${title}`);
        
        // 检查基本元素
        const sidebar = await page.locator('#sidebar').isVisible();
        console.log(`📋 侧边栏可见: ${sidebar}`);
        
        const chatMessages = await page.locator('#chatMessages').isVisible();
        console.log(`💬 聊天区域可见: ${chatMessages}`);
        
        const messageInput = await page.locator('#messageInput').isVisible();
        console.log(`⌨️ 输入框可见: ${messageInput}`);
        
        // 检查智能上下文区域
        const contextSection = await page.locator('.context-section').isVisible();
        console.log(`🧠 智能上下文区域可见: ${contextSection}`);
        
        if (contextSection) {
            const sessionName = await page.locator('#currentSessionName').textContent();
            console.log(`📝 当前会话: ${sessionName}`);
            
            const contextCount = await page.locator('#contextCount').textContent();
            console.log(`📊 上下文数量: ${contextCount}`);
        }
        
        // 检查API配置
        const apiProvider = await page.locator('#apiProvider').isVisible();
        console.log(`⚙️ API配置可见: ${apiProvider}`);
        
        if (apiProvider) {
            // 选择DeepSeek
            await page.locator('#apiProvider').selectOption('deepseek');
            console.log('✅ 选择DeepSeek提供商');
            
            // 填入API密钥
            await page.locator('#apiKey').fill('sk-e146e169c9e8403198ad3a7be6c669f8');
            console.log('✅ 填入API密钥');
            
            // 保存配置
            await page.locator('#saveConfig').click();
            console.log('✅ 点击保存配置');
            
            // 等待通知
            try {
                await page.waitForSelector('.notification', { timeout: 5000 });
                console.log('✅ 配置保存通知出现');
            } catch (e) {
                console.log('⚠️ 未检测到保存通知');
            }
        }
        
        // 测试发送消息
        console.log('💬 测试发送消息...');
        await page.locator('#messageInput').fill('你好，这是一个测试消息');
        await page.locator('#sendMessageBtn').click();
        console.log('✅ 发送测试消息');
        
        // 等待用户消息出现
        try {
            await page.waitForSelector('.message.user', { timeout: 5000 });
            console.log('✅ 用户消息已显示');
        } catch (e) {
            console.log('⚠️ 用户消息未显示');
        }
        
        // 等待AI回复（较长时间）
        console.log('⏳ 等待AI回复...');
        try {
            await page.waitForSelector('.message.assistant', { timeout: 30000 });
            console.log('✅ AI回复已显示');
            
            // 检查上下文是否更新
            await page.waitForTimeout(2000);
            const newContextCount = await page.locator('#contextCount').textContent();
            console.log(`📊 更新后的上下文数量: ${newContextCount}`);
            
        } catch (e) {
            console.log('⚠️ AI回复超时或失败');
            console.log('可能的原因: API密钥无效、网络问题或服务器错误');
        }
        
        // 测试上下文管理器
        console.log('📋 测试上下文管理器...');
        await page.locator('#viewContextBtn').click();
        
        try {
            await page.waitForSelector('#contextManagerModal.show', { timeout: 5000 });
            console.log('✅ 上下文管理器打开成功');
            
            // 检查上下文列表
            const contextItems = await page.locator('.context-item').count();
            console.log(`📝 上下文项目数量: ${contextItems}`);
            
            // 关闭管理器
            await page.locator('#closeContextManager').click();
            console.log('✅ 关闭上下文管理器');
            
        } catch (e) {
            console.log('⚠️ 上下文管理器打开失败');
        }
        
        console.log('🎉 测试完成！');
        
        // 保持浏览器打开以便观察
        console.log('⏳ 保持浏览器打开20秒...');
        await page.waitForTimeout(20000);
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
    } finally {
        if (browser) {
            await browser.close();
            console.log('🏁 浏览器已关闭');
        }
    }
}

// 运行测试
simpleTest().catch(error => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
});
